<template>
  <div class="statistics-page">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - ${titleHeight}px - 10px)`, paddingTop: `${titleHeight}px`}">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="finishedText"
          @load="onLoad"
          :immediate-check="false"
          ref="listRef"
      >
        <div class="sticky-tabs">
          <van-tabs class="tab-box"
                    v-model:active="activeTab"
                    @click-tab="tabChange"
                    ref="tabsRef"
                    color="#478E87"
                    line-width="28"
                    line-height="2"
                    title-active-color="#478E87"
                    title-inactive-color="#3D3D3D">
            <van-tab :title="tab.name" :name="tab.id" v-for="(tab, index) of tabs" :key="index"/>
          </van-tabs>
          <div style="border: 1px solid #F5F5F5; margin-top: -1px;"></div>
        </div>
        <Tags v-if="activeTab === 4" :brand="brandKey" @change="handleTag"/>
        <div v-if="tableList.length && activeTab !== 4">
          <div v-for="(item, index) in tableList" :key="index">
            <div class="user-mes-box">
              <div class="user-mes" @click="handleUser(item)">
                <div class="user-rank-box flex-vhc" :class="`ranking${index+1}`">
                  <div class="user-num">{{ index + 1 }}</div>
                </div>
                <van-image
                    class="user-avatar"
                    lazy-load
                    :src="`${item?.user?.avatar}?imageView2/1/w/200/h/200/q/75/format/webp`"
                />
                <div class="user-desc">
                  <div class="flex-vc">
                    <div class="name">{{ item?.user?.name }}</div>
                  </div>
                  <div class="count-box">
                    <GradeTag :group-id="item?.user?.groupId" :uid="item?.user?.uid"
                              :userLevel="item?.user?.userLevel"/>
                    <div v-if="![1, 2].includes(item?.user?.groupId) && Number(item?.user?.uid) !== 117229"
                         class="shield-box" :class="{'shield-box-active': Number(item?.user?.diffRecent) !== 0,}">
                      <img v-if="item.user?.countRecent !== 0" class="shield-icon"
                           :src="Number(item.user?.diffRecent) === 0 ? greenShield : redShield"/>
                      <div v-if="item.user?.countRecent !== 0 && Number(item?.user?.diffRecent) !== 0"
                           class="shield-text">{{ `${item?.user?.diffRecent}%` }}
                      </div>
                    </div>
                  </div>
                  <div class="desc-box">
                    <div class="desc" v-if="item?.user?.brand">{{ `擅长：${item?.user?.brand}` }}</div>
                  </div>
                </div>
              </div>
              <div class="identify-box">
                <div class="identify-num">{{ formatWan(item.score) }}</div>
                <div>{{ activeTab === 1 ? '首鉴值' : '鉴定值' }}</div>
              </div>
            </div>
            <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
          </div>
        </div>
        <div v-if="tableList.length && activeTab === 4">
          <div v-for="(item, index) in tableList" :key="index">
            <div class="user-mes-box">
              <div class="user-mes" @click="handleEquip(item.eid)">
                <div class="user-rank-box flex-vhc" :class="`ranking${index+1}`">
                  <div class="user-num">{{ index + 1 }}</div>
                </div>
                <van-image
                    class="equip-image"
                    lazy-load
                    :src="`${item?.logo}?imageView2/1/w/200/h/200/q/75/format/webp`"
                />
                <div class="user-desc">
                  <div class="flex-vc">
                    <div class="name">{{ item.name }}</div>
                  </div>
                  <div class="desc-box">
                    <div class="desc">{{ item.desc }}</div>
                  </div>
                </div>
              </div>
              <div class="identify-box">
                <div class="identify-num">{{ formatWan(item.count) }}</div>
                <div>求鉴量</div>
              </div>
            </div>
            <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
          </div>
        </div>
        <div>
          <Loading ref="subLoadingRef" discText="加载中..." class="loading-box"/>
        </div>
        <div v-if="!tableList.length && !loading">
          <Empty class="empty-wrap"/>
        </div>
      </van-list>
    </van-pull-refresh>
    <div v-if="activeTab !== 4" class="describe-text" @click="openPopup">榜单说明</div>
    <!--    <TabBar :page="2"/>-->
    <ListDescriptionPopup ref="listDescriptionPopupRef"/>
  </div>
</template>

<script setup>
import {computed, ref, onMounted, nextTick, watch} from "vue";
import Loading from "@/components/loading/index.vue"
import TabBar from "@/components/tabbar.vue";
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import Tags from "@/views/identify/components/tags.vue";
import ListDescriptionPopup from "@/views/identify/details/components/listDescriptionPopup.vue";
import Empty from "@/components/empty/index.vue"
import identifyApi from "@/services/identify.js";
import greenShield from "@/assets/images/common/greenShield.png"
import redShield from "@/assets/images/common/redShield.png"
import {getQueryParam, isUserGroup, jumpUser, formatWan} from "@/utils/common.js";
import {useRoute, useRouter} from "vue-router";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();
const router = useRouter()

const route = useRoute()


const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const refreshing = ref(false)
const loading = ref(false)
const subLoadingRef = ref()
const listDescriptionPopupRef = ref()
const finished = ref(false)
const listRef = ref()
const tableList = ref([]);
const finishedText = ref('没有更多了')
const activeTab = ref()
const brandKey = ref(-1)
const params = ref({
  page: 1,
  pageSize: 10,
})
const tabs = ref([
  {
    name: "首鉴榜",
    id: 1,
  }, {
    name: "热心榜",
    id: 2,
  }, {
    name: "求鉴榜",
    id: 4,
  }
])

const shareData = ref([
  {
    name: "中羽鉴定 首鉴榜",
    id: 1,
    desc: '先锋鉴定榜单'
  }, {
    name: "中羽鉴定 热心榜",
    id: 2,
    desc: '热心鉴定榜单'
  }, {
    name: "中羽鉴定 求鉴榜",
    id: 4,
    desc: '热门装备求鉴榜单'
  }
])


const handleUser = (item) => {
  jumpUser(item.user.uid)
}

const handleEquip = (eid) => {
  //跳转到装备详情
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/related/index?mag_hide_progress=1`, {
      eid: eid,
    });
  } else {
    router.push({
      name: 'related',
      query: {
        eid: eid,
      }
    })
  }
}


const openPopup = () => {
  listDescriptionPopupRef.value.show()
}

const handleTag = (item) => {
  console.log('item', item)
  params.value.page = 1;
  tableList.value = [];
  brandKey.value = item.id
  getPageList()
}

const tabChange = (item) => {
  brandKey.value = -1
  activeTab.value = item.name
  params.value.page = 1
  tableList.value = []
  getPageList()
}


const onRefresh = () => {
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getPageList();
  })
}

const onLoad = () => {
  if (tableList.value) {
    params.value.page++
    nextTick(() => {
      getPageList();
    })
  }
};

const getPageList = () => {
  let paramsCopy = {
    ...params.value,
    type: activeTab.value,
    brand: Number(brandKey.value) === -1 ? '' : brandKey.value
  };
  loading.value = true;
  identifyApi.getRanking(paramsCopy).then((res) => {
    if (res.code === 200) {
      if (refreshing.value) {
        tableList.value = [];
        refreshing.value = false;
        finished.value = false;
      }
      let data = res.data || {};
      let dataList = data.list || [];
      let records = dataList || [];
      tableList.value = tableList.value.concat(records);
      let total = data.total || 0;
      if (total <= tableList.value.length) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = "没有更多了";
      if (tableList.value.length === 0) {
        finishedText.value = "";
      }
    } else {
    }
  }).finally(() => {
    loading.value = false;
    subLoadingRef.value.hide()
  })
}


const setShare = () => {
  const matchedTab = shareData.value.find(tab => tab.id == activeTab.value);
  const title = matchedTab ? matchedTab.name : '中羽装备鉴定榜单'; // 处理未找到的情况
  const des = matchedTab ? matchedTab.desc : '首鉴榜  热心榜 求鉴榜'; // 处理未找到的情况
  window.mag.setData({
    shareData: {
      title: title,
      des: des,
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: `${window.location.href}&activeTab=${activeTab.value}`,
    }
  });
}

watch(() => activeTab.value,
    (val) => {
      if(activeTab.value) {
        setShare()
      }
    })

onMounted(async () => {
  let type = route.query.activeTab || getQueryParam('activeTab') || 1
  activeTab.value = Number(type)
  setShare()
  getPageList()
  subLoadingRef.value.show()
  await isUserGroup()
})


if (authStore.phone) {
  window.mag.setTitle('榜单');
} else {
  document.title = '榜单'
}
window.mag.showNavigation()
</script>

<style scoped lang="scss">
.statistics-page {
  height: 100%;
  width: 100vw;
  background-color: #FFFFFF;

  .sticky-tabs {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .content-table-list {
    //height: 404px;
    overflow: auto;

    .user-mes-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 12px;

      .user-mes {
        display: flex;
        align-items: center;

        .user-rank-box {
          width: 24px;
          height: 24px;
          margin-right: 6px;
          background-image: url("@/assets/images/details/ranking4.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;

          .user-num {
            color: #FFFFFF;
            font-size: 14px;
            font-weight: 600;
          }
        }

        .ranking1 {
          background-image: url("@/assets/images/details/ranking1.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
        }

        .ranking2 {
          background-image: url("@/assets/images/details/ranking2.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
        }

        .ranking3 {
          background-image: url("@/assets/images/details/ranking3.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
        }

        .user-avatar {
          width: 40px;
          height: 40px;
          overflow: hidden;
          border-radius: 26px;
          margin-right: 8px;
        }

        .equip-image {
          flex-shrink: 0;
          width: 40px;
          height: 40px;
          overflow: hidden;
          border-radius: 8px;
          margin-right: 8px;
          border: 1px solid #F5F5F5;
        }

        .user-desc {
          .name {
            color: #3D3D3D;
            font-size: 14px;
          }

          .ratio {
            color: #FA5151;
            font-size: 10px;
            padding: 2px 3px;
            margin-bottom: 2px;
            border-radius: 3px;
            margin-left: 5px;
            background: #FFEEEE;
          }

          .count-box {
            margin-top: 5px;
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 11px;
            color: #999999;
          }

          .desc-box {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 10px;
            margin-top: 2px;

            .tag {
              color: #FFFFFF;
              padding: 2px 4px;
              border-radius: 3px;
              background-color: #819FFF;
              margin-right: 5px;
            }

            .pink {
              background-color: #FF7CAA;
            }

            .purple {
              background-color: #819FFF;
            }

            .orange {
              background-color: #FF8F1F;
            }

            .golden {
              color: #3D3D3D;
              font-weight: 500;
              border: 1px solid #FFE3B9;
              background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
            }

            .green {
              background-color: #00AC72;
            }

            .desc {
              padding-top: 3px;
              color: #999999;
            }
          }
        }
      }

      .identify-box {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        color: #A8A8A8;
        font-size: 10px;
        font-weight: 400;
        margin-left: 32px;
        flex-shrink: 0;

        .identify-num {
          color: #478E87;
          font-size: 17px;
          font-weight: 700;
          padding-bottom: 3px;
        }
      }
    }

    .line-divider {
      ::v-deep.van-divider {
        margin: 0;
        padding-left: 90px;
      }
    }
  }
}

.describe-text {
  position: fixed;
  bottom: 0;
  width: 100vw;
  text-align: center;
  color: #666666;
  font-size: 14px;
  padding: 6px 0 26px;
  background-color: #FFFFFF;
}

.empty-wrap {
  padding-top: 50%;
  height: 40vh;
}

::v-deep .tags-page {
  margin: 10px 12px 0 !important;
}
</style>