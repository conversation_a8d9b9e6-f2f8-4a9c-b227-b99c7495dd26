<template>
  <div class="related-page" :style="{ paddingTop: `${titleHeight}px`}">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      @scroll="handleScrollBtn"
                      :style="{ height: `calc(100vh - 10px -  ${footerBtnHeight}px - ${titleHeight}px)`}">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="finishedText"
          @load="onLoad"
          scroller
          ref="listRef"
          :immediate-check="false"
          :error.sync="errorStatus"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
      >
        <div class="equip-container" @click="jumpEquip(eid)">
          <div class="equip-mes">
            <div class="equip-mes-left">
              <img class="equip-img" :src="`${equipDetail.pic}?imageView2/1/w/200/h/200/q/75/format/webp`"/>
              <div class="equip-text">
                <div class="equip-name">{{ equipDetail?.name }}</div>
                <div>{{ `${equipDetail?.brand} ${equipDetail?.cate}` }}</div>
              </div>
            </div>
            <div class="equip-mes-right">
              <div class="score-number mb-5">{{ equipDetail?.point }}</div>
              <van-rate
                  v-model="startValue"
                  :size="16"
                  readonly
                  color="#ffd21e"
                  void-icon="star"
                  void-color="#eee"
              />
              <div class="score-tips mt-8">中羽评分</div>
            </div>
          </div>
          <div class="equip-line"></div>
          <div class="equip-count">
            <div class="item-count">
              <div>{{ formatWan(equipDetail?.total) }}</div>
              <div class="item-text">鉴定总数</div>
            </div>
            <div class="item-count">
              <div>{{ formatWan(equipDetail?.real) }}</div>
              <div class="item-text">鉴定看正</div>
            </div>
            <div class="item-count">
              <div>{{ formatWan(equipDetail?.fake) }}</div>
              <div class="item-text">鉴定看假</div>
            </div>
            <div class="item-count">
              <div>{{ equipDetail?.rate }}<span style="font-size: 12px">%</span></div>
              <div class="item-text">鉴定假率</div>
            </div>
          </div>
        </div>
        <div v-show="scrollDistance > 150" class="sticky-equip"
             :class="{ 'sticky-equip--visible': scrollDistance > 150 }">
          <div class="sticky-equip-box">
            <img class="equip-img" :src="`${equipDetail.pic}?imageView2/1/w/200/h/200/q/75/format/webp`"/>
            <div class="sticky-equip-text">
              <div class="equip-name">{{ equipDetail?.name }}</div>
              <div class="equip-cate">{{ `${equipDetail?.brand} ${equipDetail?.cate}` }}</div>
            </div>
          </div>
        </div>
        <div class="title-top">
          <TitleTop :title="`共有${formatWan(total)}条`" :state="state" :user-mes="userMes" :switchIcon="switchIcon"
                    :switchType="switchType"
                    :equipType="true" @change="handleScreen" @sort="handleSort" @compose="handleCompose"/>
        </div>
        <div v-if="tableList.length" class="ml-12 mr-12 mt-12">
          <List v-if="!switchType" :listType="listType" :tableList="tableList"/>
          <GridList v-if="switchType" :listType="listType" :tableList="tableList"/>
        </div>
        <div>
          <Loading ref="subLoadingRef" discText="加载中..." class="loading-box"/>
        </div>
        <div v-if="!tableList.length && !loading">
          <Empty class="empty-wrap"/>
        </div>
      </van-list>
    </van-pull-refresh>
    <div v-if="btnShow" class="footer-btn" :class="{'ios-bar': IS_IOS}" @click="handleHome">
      <text class="footer-text">其他求鉴</text>
    </div>
    <div class="posted">
      <view v-if="allBtn" class="all-btn" @click="handleRelease">
        <img class="edit-icon" :src="editIcon"/>
        <text>求鉴</text>
      </view>
      <view v-else key="half-btn" class="half-btn">
        <img class="edit-icon" :src="editIcon"/>
      </view>
    </div>
  </div>
</template>

<script setup>
import {nextTick, ref, onMounted, onUnmounted, watch, computed} from "vue";
import TitleTop from "@/components/titleTop.vue";
import Loading from "@/components/loading/index.vue"
import List from "@/views/identify/components/list.vue"
import GridList from "@/views/identify/components/gridList.vue"
import editIcon from "@/assets/images/common/editIcon.png"
import {useAuthStore} from "@/stores/auth.js";
import {getCache} from "@/utils/cache.js";
import identifyApi from "@/services/identify.js";
import {formatWan, getQueryParam, isIOS} from "@/utils/common.js";
import {useRoute, useRouter} from "vue-router";
const router = useRouter()

const route = useRoute()
const IS_IOS = isIOS()

const authStore = useAuthStore();

const switchType = computed(() => {
  return authStore.switchType
})

let scrollTimeout; // 用于检测滚动是否停止
const allBtn = ref(true)
const btnShow = ref(false)
const listType = ref('equipList')
const equipDetail = ref({})
const startValue = ref(4)
const eid = ref()
const scrollDistance = ref(0)
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const footerBtnHeight = ref(0)
const refreshing = ref(false)
const loading = ref(false)
const subLoadingRef = ref()
const finished = ref(false)
const listRef = ref()
const finishedText = ref('')
const error = ref(false)
const errorStatus = ref(false)
const tableList = ref([]);
const total = ref(0)
const params = ref({
  page: 1,
  pageSize: 10,
})
const order = ref(1)
const appraisal = ref(0)
const userMes = ref()
const state = ref(0)
const switchIcon = ref(true)


const handleCompose = () => {
  authStore.switchType = !authStore.switchType
}


const handleRelease = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/release/index?mag_hide_progress=1&needlogin=1`, {
      brandId: equipDetail.value?.brandId ,
      categoryId: equipDetail.value?.cateId ,
      eaName: encodeURIComponent(equipDetail.value?.name),
      eid: equipDetail.value?.eid,
    })
  } else {
    router.push({
      name: 'release',
      query: {
        brandId: equipDetail.value?.brandId ,
        categoryId: equipDetail.value?.cateId ,
        eaName: encodeURIComponent(equipDetail.value?.name),
        eid: equipDetail.value?.eid,
      }
    })
  }
}


const handleHome = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/identify/index?mag_hide_progress=1&mag_sapp_style=1`);
  } else {
    router.push({
      name: 'identify',
    })
  }
}

const jumpEquip = (eid) => {
  if (!authStore.phone) {
    window.location.href = `https://www.badmintoncn.com/eqm.php?a=view&eid=${eid}`;
  } else {
    window.mag.newWin(`https://www.badmintoncn.com/eqm.php?a=view&eid=${eid}&mag_hide_progress=1&mag_sapp_style=1&themecolor=30BC9D`)
  }
}

const handleScreen = (val) => {
  subLoadingRef.value.show()
  appraisal.value = val
  params.value.page = 1;
  tableList.value = []
  getPageList();
}

const handleSort = (val) => {
  subLoadingRef.value.show()
  order.value = val
  tableList.value = []
  params.value.page = 1;
  getPageList();
}

const onTouchMove = (event) => {
  allBtn.value = false
}

const onTouchEnd = () => {
  allBtn.value = true
}

const handleScrollBtn = (scroll) => {
  clearTimeout(scrollTimeout);
  allBtn.value = false; // 滚动时设置为 false
  scrollTimeout = setTimeout(() => {
    allBtn.value = true; // 停止滚动后设置为 true
  }, 200); // 短暂延迟以确保滚动完全停止
}


const onRefresh = () => {
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getPageList();
  })
}

const onLoad = () => {
  if (tableList.value) {
    params.value.page++
    nextTick(() => {
      getPageList();
    })
  }
};


const getPageList = () => {
  let paramsCopy = {
    ...params.value,
    id: eid.value,
    brand: null,
    appraisal: appraisal.value === 0 ? null : appraisal.value,
    order: order.value,
  };
  loading.value = true
  identifyApi.getEquip(paramsCopy).then((res) => {
    if (res.code === 200) {
      if (refreshing.value) {
        tableList.value = [];
        refreshing.value = false;
        finished.value = false;
      }
      let data = res.data || {};
      equipDetail.value = data.list.equip
      startValue.value = equipDetail.value?.star
      let dataList = data.list?.list || [];
      let records = dataList || [];
      tableList.value = tableList.value.concat(records);
      total.value = data.total || 0;
      if (total.value <= tableList.value.length || res.data.list === null) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = "没有更多了";
      if (tableList.value.length === 0) {
        finishedText.value = "";
      }
    } else {
      errorStatus.value = true;
    }
  }).finally(() => {
    loading.value = false
    subLoadingRef.value.hide()
  })
}

// 滚动处理函数
const handleScroll = () => {
  const container = document.querySelector('.content-table-list')
  if (container) {
    scrollDistance.value = container.scrollTop
  }
}

watch(() => equipDetail.value,
    () => {
      window.mag.setData({
        shareData: {
          title: `${equipDetail.value?.name}的相关求鉴`,
          des: '羽毛球器材鉴定，羽毛球装备鉴定，真假鉴别',
          // picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
          picurl:`${equipDetail.value?.pic}?imageView2/1/w/200/h/200/q/75/format/webp`,
          linkurl: window.location.href,
        }
      });
    }, {
      immediate: true,
      deep: true
    })

onMounted(async () => {
  eid.value = getQueryParam('eid') || route.query.partition
  let eqId = getQueryParam('eq') || route.query.eq
  btnShow.value = Number(eqId) === 1
  userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
  subLoadingRef.value.show()
  await getPageList()
  const container = document.querySelector('.content-table-list')
  if (container) {
    container.addEventListener('scroll', handleScroll)
  }
  const footerBtn = document.querySelector('.footer-btn');
  if (footerBtn) {
    footerBtnHeight.value = footerBtn.offsetHeight;
  }
  if (authStore.phone) {
    window.mag.setTitle('相关求鉴');
  } else {
    document.title = '相关求鉴'
  }
  window.mag.showNavigation()
  window.mag.setData({
    shareData: {
      title: `${equipDetail.value?.name}的相关求鉴`,
      des: '羽毛球器材鉴定，羽毛球装备鉴定，真假鉴别',
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: window.location.href,
    }
  });
})

onUnmounted(() => {
  const container = document.querySelector('.content-table-list')
  if (container) {
    container.removeEventListener('scroll', handleScroll)
  }
})

</script>

<style lang="scss" scoped>
.related-page {
  position: relative;
  width: 100vw;
  height: 100%;
  overflow: hidden;
  background-color: #FFFFFF;

  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
}

.sticky-equip {
  position: fixed; // 使用 fixed 定位，性能更好，且不占用文档流空间
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  transform: translateY(-100%); // 默认向上移动自身高度，完全隐藏
  opacity: 0;
  background-color: #FFFFFF;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
  border-radius: 5px;

  .sticky-equip-box {
    display: flex;
    align-items: center;
    padding: 10px 12px;

    .equip-img {
      width: 34px;
      height: 34px;
      margin-right: 10px;
      border-radius: 3px;
      border: 1px solid #F5F5F5;
    }

    .sticky-equip-text {
      color: #3D3D3D;
      flex: 1;
      min-width: 0;

      .equip-name {
        font-size: 14px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .equip-cate {
        margin-top: 8px;
        font-size: 10px;
      }
    }
  }

}

.sticky-equip--visible {
  // 当添加这个类时，元素回到原位并变得不透明
  transform: translateY(0);
  opacity: 1;
}

.equip-container {
  margin: 12px;
  border-radius: 5px;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);

  .equip-mes {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .equip-mes-left {
      display: flex;
      align-items: center;

      .equip-img {
        width: 56px;
        height: 56px;
        margin-right: 10px;
        border-radius: 5px;
        border: 1px solid #F5F5F5;
      }

      .equip-text {
        font-weight: 400;
        font-size: 10px;
        color: #3D3D3D;
        height: 54px;
        display: flex;
        flex-direction: column;
        //justify-content: space-between;
        justify-content: space-around;
      }

      .equip-name {
        font-weight: 500;
        font-size: 14px;
        line-height: 1.2;
      }
    }

    .equip-mes-right {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      flex-direction: column;

      .score-number {
        color: #FF5900;
        font-size: 22px;
        font-weight: 700;
      }

      .score-tips {
        font-size: 10px;
        color: #999999;
        font-weight: 400;
      }
    }

  }

  .equip-line {
    margin: 0 10px;
    border: 1px dashed #EDEDED;
  }

  .equip-count {
    padding: 13px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item-count {
      display: flex;
      align-items: center;
      flex-direction: column;
      font-size: 16px;
      color: #3D3D3D;
      font-weight: 600;

      .item-text {
        margin-top: 8px;
        color: #666666;
        font-weight: 400;
        font-size: 10px;
      }
    }
  }
}

.footer-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 44px;
  margin: 0 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 22px;
  background-color: #DFF7F6;
  z-index: 999;
  .footer-text {
    color: #478E87;
    font-size: 16px;
    font-weight: 600;
  }
}

.ios-bar {
 margin: 0 12px 20px;
}

.posted {
  position: absolute;
  right: 0;
  top: 75%;
  z-index: 1;

  .edit-icon {
    width: 20px;
    height: 20px;
    margin-right: 2px;
  }

  .all-btn {
    width: 76px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 14px;
    margin-right: 12px;
    background-color: #000000;
    border-radius: 30px;
  }

  .half-btn {
    width: 44px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 14px;
    background-color: #000000;
    border-radius: 20px 0 0 20px;
  }
}

.empty-wrap {
  padding-top: 40%
}

::v-deep .loading-page {
  top: 50%;
}

.title-top {
  margin: 20px 12px 0;
}

::v-deep .title-name {
  font-weight: 600;
}

::v-deep .van-rate__item {
  padding-right: 0;
}
</style>