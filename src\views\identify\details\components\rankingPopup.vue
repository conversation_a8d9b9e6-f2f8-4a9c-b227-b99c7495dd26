<template>
  <van-popup v-if="rankingPopup" :show="rankingPopup" round position="bottom" @click-overlay="hide">
    <div class="ranking-popup">
      <div class="ranking-title">
        <div>{{ `目前${rankingTotal}人表态${rankingType}` }}</div>
        <img class="rank-icon" :src="rankingIcon" @click="handleStat"/>
      </div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
            :immediate-check="false"
            :error.sync="errorStatus"
            ref="listRef"
        >
          <div>
            <div v-for="(item, index) in list" :key="index">
              <div class="user-mes-box">
                <div class="user-mes" @click="handleUser(item)">
                  <img class="user-avatar" :src="item?.user?.avatar"/>
                  <div class="user-desc">
                    <div class="flex-vc">
                      <div class="name">{{ item?.user?.name }}</div>
                      <div v-if="item?.train" class="train-tag">训</div>
                    </div>
                    <div class="count-box">
                      <GradeTag :group-id="item?.user?.groupId" :uid="item?.user?.uid"
                                :userLevel="item?.user?.userLevel"/>
                      <div v-if="![1, 2].includes(item?.user?.groupId) && Number(item?.user?.uid) !== 117229"
                           class="shield-box" @click="handleRatio(item)"
                           :class="{'shield-box-active': Number(item?.user?.diffRecent) !== 0}">
                        <img v-if="item.user?.countRecent !== 0" class="shield-icon"
                             :src="Number(item.user?.diffRecent) === 0 ? greenShield : redShield"/>
                        <div v-if="item.user?.countRecent !== 0 && Number(item?.user?.diffRecent) !== 0"
                             class="shield-text">{{ `${item?.user?.diffRecent}%` }}
                        </div>
                      </div>
                    </div>
                    <div class="desc-box">
                      <div class="desc" v-if="item?.user?.brand">{{ `擅长：${item.user?.brand}` }}</div>
                    </div>
                  </div>
                </div>
                <div class="time-mes">
                  <div class="time">{{ item.regretTime ? `修改于${item.regretTime}` : item.createTime }}</div>
                  <div class="time-mes-tag">
                    <img v-if="item?.redQ && item?.redClaim" class="hb-image mr-1" :src="hbIcon"/>
                    <div v-if="item.redAmount > 0 && item.user.uid === userInfo.id && item.redClaim"
                         class="money-text mr-5"><span
                        style="font-size: 10px">¥</span>{{ Number(item?.redAmount).toFixed(2) }}
                    </div>
                    <div v-if="item?.first" class="tag-box relative purple-tag mr-5">首鉴</div>
                    <div v-if="item.tag === '嫁接'" class="tag-box mr-5">{{ item.tag }}</div>
                    <div v-if="item.tag === '修复'" class="tag-box orange-tag mr-5">{{ item.tag }}</div>
                    <div v-if="item.tag === '翻新'" class="tag-box green-tag mr-5">{{ item.tag }}</div>
                    <div v-if="item.tag === '拼图'" class="tag-box blue-tag mr-5">{{ item.tag }}</div>
                    <div v-if="item?.regret" class="tag-box yellow-tag mr-5">改</div>
                  </div>
                </div>
              </div>
              <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </van-popup>
</template>

<script setup>
import {ref, computed, nextTick} from "vue";
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import {getCache} from "@/utils/cache.js";
import rankingIcon from "@/assets/images/details/ranking.png"
import greenShield from "@/assets/images/common/greenShield.png"
import redShield from "@/assets/images/common/redShield.png"
import hbIcon from "@/assets/images/reward/hbIcon.png"
import avatarIcon from "@/assets/images/identify/avatarIcon.png"
import {useRouter} from "vue-router";
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {jumpUser} from "@/utils/common.js";

const userInfo = computed(() => {
  return authStore.userInfo
})


const authStore = useAuthStore()

const router = useRouter()


const typeId = ref()
const rankingPopup = ref(false)
const rankingType = ref()
const rankingTotal = ref(0)
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const listRef = ref()
const list = ref([]);
const finishedText = ref('没有更多了')
const errorStatus = ref(false)
const params = ref({
  page: 1,
  pageSize: 10,
})

const handleUser = (item) => {
  jumpUser(item.user.uid)
}

const handleRatio = (item) => {
  // if(Number(item?.user?.diffRecent) > 0) {
  //   showToast('表态少数方占比')
  // } else {
  //   showToast('表态多数方占比')
  // }
}


const userGroupList = computed(() => {
  return JSON.parse(getCache('USER_GROUP_LIST')).reduce((obj, item) => {
    obj[item.groupId] = item
    return obj
  }, {})
})


const handleStat = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/details/statistics?mag_hide_progress=1`);
  } else {
    router.push({
      name: 'statistics'
    })
  }
}


const onRefresh = () => {
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getPageList();
  })
}

const onLoad = () => {
  if (list.value) {
    params.value.page++
    nextTick(() => {
      getPageList();
    })
  }
};

const getPageList = () => {
  let paramsCopy = {
    ...params.value,
    tid: typeId.value,
  };
  loading.value = true
  identifyApi.getAppraisal(paramsCopy).then((res) => {
    if (res.code === 200) {
      if (refreshing.value) {
        list.value = [];
        refreshing.value = false;
        finished.value = false;
      }
      let data = res.data || {};
      let dataList = rankingType.value === '看正' ? data.list.real.list : data.list.fake.list;
      let records = dataList || [];
      list.value = list.value.concat(records);
      let total = rankingType.value === '看正' ? data.list.real.total : data.list.fake.total;
      rankingTotal.value = total
      if (total <= list.value.length) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = "没有更多了";
      if (list.value.length === 0) {
        finishedText.value = "";
      }
    } else {
      errorStatus.value = true;
    }
  }).finally(() => {
    loading.value = false
  })
}

const open = (type, id) => {
  params.value.page = 1
  finished.value = false
  loading.value = false
  refreshing.value = false
  errorStatus.value = false
  list.value = []
  typeId.value = id
  rankingType.value = type === 1 ? '看正' : '看假'
  rankingPopup.value = true
  getPageList()
}

const hide = () => {
  loading.value = false
  rankingPopup.value = false
}

defineExpose({open, hide,})

</script>

<style scoped lang="scss">
.ranking-popup {
  color: #3D3D3D;
  margin: 16px 0;

  .ranking-title {
    margin: 0 12px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .rank-icon {
      width: 24px;
      height: 24px;
    }
  }

  .content-table-list {
    height: 404px;
    overflow: auto;

    .user-mes-box {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 11px 12px;

      .user-mes {
        display: flex;
        align-items: center;

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 26px;
          margin-right: 8px;
        }

        .user-desc {
          .name {
            color: #3D3D3D;
            font-size: 14px;
          }

          .train-tag {
            color: #FFFFFF;
            font-weight: 400;
            font-size: 10px;
            padding: 3px;
            margin-left: 3px;
            background-color: #9673FF;
            border-radius: 3px;
          }

          .count-box {
            padding-top: 4px;
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 11px;
            color: #999999;

            .ratio {
              color: #FA5151;
              font-size: 10px;
              padding: 2px 4px;
              margin-bottom: 2px;
              border-radius: 3px;
              margin-left: 5px;
              background: #FFEEEE;
            }
          }

          .desc-box {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 10px;
            margin-top: 2px;

            .tag {
              color: #FFFFFF;
              padding: 1px 3px;
              border-radius: 3px;
              background-color: #819FFF;
              margin-right: 5px;
            }

            .pink {
              background-color: #FF7CAA;
            }

            .purple {
              background-color: #819FFF;
            }

            .orange {
              background-color: #FF8F1F;
            }

            .golden {
              color: #3D3D3D;
              font-weight: 500;
              border: 1px solid #FFE3B9;
              background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
            }

            .green {
              background-color: #00AC72;
            }

            .desc {
              padding-top: 3px;
              color: #999999;
            }
          }
        }
      }

      .time-mes {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .time {
          color: #A8A8A8;
          font-size: 10px;
          font-weight: 400;
          padding-top: 4px;
        }

        .time-mes-tag {
          display: flex;
          align-items: center;
          margin-top: 6px;
        }

        .tag-box {
          color: #FA5151;
          font-size: 10px;
          font-weight: 600;
          border-radius: 5px;
          padding: 4px 5px;
          background-color: #FFECEC;
        }

        .hb-image {
          width: 22px;
          height: 22px;
        }

        .money-text {
          font-weight: 600;
          font-size: 13px;
          color: #FF3700;
        }

        .purple-tag {
          padding: 3px 4px;
          color: #9673FF;
          background-color: #FFFFFF;
        }

        .purple-tag::after {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          width: 200%;
          height: 200%;
          border-radius: 10px;
          border: 1px solid #9673FF;
          transform: scale(0.5);
          transform-origin: 0 0;
          box-sizing: border-box;
        }

        .orange-tag {
          color: #FF7F00;
          background-color: #FFF1E3;
        }

        .green-tag {
          color: #00B578;
          background-color: #D7F7EC;
        }

        .blue-tag {
          color: #13A2DB;
          background-color: #DBF5FF;
        }

        .yellow-tag {
          color: #3D3D3D;
          font-weight: 700;
          background-color: #FFF64F;
        }
      }
    }

    .line-divider {
      ::v-deep.van-divider {
        margin: 0;
        padding-left: 60px;
      }

      //
    }
  }
}
</style>