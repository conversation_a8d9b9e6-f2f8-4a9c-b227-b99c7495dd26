<template>
  <van-popup
      v-model:show="rewardShow"
      round
      position="bottom"
  >
    <div class="list-popup">
      <div class="title-box">
        <div></div>
        <div>奖励规则</div>
        <div></div>
      </div>
      <div class="content-box pt-20 pb-5">①近期首鉴次数≥14次且鉴定量≥70次内含首鉴</div>
      <div class="content-box pb-5">②鉴定等级需≥热心四级</div>
      <div class="content-box">③近期异鉴率≤0.5%</div>
      <div class="content-box pt-20 content-text">红包奖励：红包个数来源求鉴者的奖励，同时满足上方三个鉴定条件，参与鉴定的有机会获得随机红包奖励。</div>
      <div class="content-box pt-20 pb-5">时间范围：</div>
      <div class="content-box content-text">①根据求鉴者选择的红包个数对应不同开奖时间</div>
      <div class="content-box content-text">②开奖前参与鉴定有机会获得随机红包奖励；若开奖后参与鉴定的按普通鉴定算</div>
      <div class="content-box content-text">③开奖后满足奖励规则的参与鉴定可在6小时内抽取随机红包奖励，若抽奖时间结束还有剩余红包系统将自动分配</div>
      <div class="content-box content-text pt-20 pb-25">鉴定结果：开奖前表态后可见，开奖后公开可见</div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from "vue";


const rewardShow = ref(false)
const emit = defineEmits(['close'])


const changeKnow = () => {
  hide()
  emit('close')
}


const show = () => {
  rewardShow.value = true
}
const hide = () => {
  rewardShow.value = false
}


defineExpose({show, hide})

</script>

<style lang="scss" scoped>
.list-popup {
  margin: 15px 12px 20px;
  box-sizing: border-box;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .title-tips {
    margin-top: 20px;
    color: #3D3D3D;
    line-height: 1.4;
    font-size: 15px;
  }

  .content-box {
    font-size: 14px;
    color: #3D3D3D;
    font-weight: 400;
  }

  .content-text {
    line-height: 1.4;
  }

  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>