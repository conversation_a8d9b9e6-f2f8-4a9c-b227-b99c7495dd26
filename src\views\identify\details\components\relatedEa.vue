<template>
  <div class="related-box" v-if="tableList?.length">
    <div class="related-title">
      <div class="left-title">
        <img class="title-img" :src="xgEaIcon"/>
        <div class="title-count">{{`求鉴量：${formatWan(tableTotal)}`}}</div>
      </div>
      <div class="right-title">
        <div class="mr-2">去看看</div>
        <van-icon name="arrow" size="11"/>
      </div>
    </div>
    <div class="related-table">
      <div class="table-list-container">
        <template v-for="(val,index) in tableList">
          <div class="table-list-item">
            <div class="image-bg">
              <van-image v-if="val.picture.length" class="image-box" fit="cover" lazy-load
                         :src="`${val.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`"/>
              <img v-if="!val.picture.length" class="image-box" :src="hollowIcon"/>
              <div class="image-top" v-if="val.real + val.fake !== 0 && !userMes?.train">
                <div class="top-people" :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                  <img class="jd-icon" :src="shieldIcon"/>
                  <div class="pr-4">{{ val.real + val.fake }}</div>
                </div>
                <div class="top-ratio" v-if="val.real + val.fake >= 2"
                     :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                  {{ getDominantPercentage(val.real, val.fake) }}
                  <span class="per-text">%</span>
                </div>
              </div>
              <div v-if="val?.train" class="image-top-right">训</div>
              <img v-if="val.appraisal !== null" class="verified-icon" :src="verifiedIcon"/>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, watch} from "vue";
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import verifiedIcon from "@/assets/images/identify/verifiedIcon.png"
import hollowIcon from "@/assets/images/common/hollowIcon.png"
import xgEaIcon from "@/assets/images/details/xgqj.png"
import identifyApi from "@/services/identify.js";
import {formatWan} from "@/utils/common.js";


const props = defineProps({
  userMes: {
    type: Object,
    default: () => {
    }
  },
  eid: {
    type: Number,
    default: 0
  },
  tid: {
    type: String,
    default: ''
  }
})

const tableList = ref([])
const tableTotal = ref(0)


const emit = defineEmits(['equipList'])


// 获取背景颜色
const getBackgroundColor = (real, fake) => {
  if (real + fake === 0) return '';
  if (real > fake) {
    return '#00B578';
  } else if (fake > real) {
    return '#FA5151';
  } else {
    return '#676f6e';
  }
};

// 获取主导方的百分比
const getDominantPercentage = (real, fake) => {
  const total = real + fake;
  if (total === 0) return ''; // 如果总和为 0，返回空字符串
  if (real > fake) {
    return `${Math.round((real / total) * 100)}`; // 看正的比率
  } else if (fake > real) {
    return `${Math.round((fake / total) * 100)}`; // 看假的比率
  } else {
    return '50'; // 如果相等，返回空字符串
  }
};


watch(() => props.eid,
    (val) => {
      if (val) {
        getTableList()
      }
    }, {
      immediate: true,
      deep: true
    })

// const getTableList = () => {
//   let paramsCopy = {
//     page: 1,
//     pageSize: 4,
//     id: props?.eid,
//     order: 2,
//   };
//   identifyApi.getEquip(paramsCopy).then((res) => {
//     if (res.code === 200) {
//       tableList.value = res.data?.list?.list || []
//       tableTotal.value = res.data?.total || 0
//     }
//   })
// }

const getTableList = () => {
  const paramsCopy = {
    page: 1,
    pageSize: 4,
    id: props?.eid,
    order: 2,
  };
  identifyApi.getEquip(paramsCopy).then((res) => {
    if (res.code === 200) {
      const list = res.data?.list?.list || [];
      const hasTargetItem = list.some(item => Number(item.tid) === Number(props.tid));
      let result;
      if (hasTargetItem) {
        result = list.filter(item => Number(item.tid) !== Number(props.tid));
      } else {
        result = list.slice(0, 3);
      }
      tableList.value = result;
      emit('equipList', tableList.value )
      tableTotal.value = res.data?.total || 0;
    }
  });
};


</script>

<style lang="scss" scoped>
.related-box {
  margin: 15px 12px 0;

  .related-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-title {
      display: flex;
      align-items: center;
      .title-img {
        width: 76px;
        height: 18px;
      }
      .title-count {
        margin-top: 2px;
        margin-left: 5px;
        padding: 4px 5px;
        color: #666666;
        font-size: 10px;
        font-weight: 400;
        border-radius: 3px;
        background-color: #F5F5F5;
      }
    }
    .right-title {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666666;
      font-weight: 400;
    }
  }

  .related-table {
    margin-top: 15px;

    .table-list-container {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      column-gap: 9px;

      .table-list-item {
        width: calc((100% - 18px) / 3);

        .image-bg {
          position: relative;
          width: 111px;
          height: 111px;
          //background-color: pink;
          border-radius: 5px;
          overflow: hidden;

          .image-box {
            width: 111px;
            height: 111px;
            object-fit: cover;
            border-radius: 5px;
          }

          .image-top-right {
            position: absolute;
            top: 0;
            right: 0;
            color: #FFFFFF;
            font-size: 13px;
            padding: 3px;
            background-color: #9673FF;
            border-radius: 0 5px 0 5px;
          }

          .background-layer {
            position: absolute;
            display: flex;
            align-items: center;
            bottom: 0;
            height: 28px;
            width: 100%;
            border-radius: 0 0 5px 5px;
            background: rgba(0, 0, 0, 0.1);
            /* 关键模糊代码 */
            filter: blur(20px);
            /* 提升模糊效果 */
            transform: translateZ(0); /* 启用GPU加速 */
            -webkit-backface-visibility: hidden; /* 修复模糊边缘问题 */
          }

          .verified-icon {
            position: absolute;
            top: 46%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 110px;
            height: 110px;
            z-index: 1;
            opacity: 0.8;
            border-radius: 50%;
          }
        }
      }
    }
  }
}
</style>