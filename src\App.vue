<script setup>
import TitleTips from "@/views/titleTips.vue";
import {RouterView} from 'vue-router'
import {onMounted, onUnmounted, ref} from "vue";
import {debounce} from "lodash";
import axios from "axios";

const isMagApp = window.navigator.appVersion.toLowerCase().indexOf('magappx') != -1
const isPC = !/iPhone|iPod|iPad|Android|BlackBerry|IEMobile|Opera Mini|Mobile|Tablet/i.test(navigator.userAgent)
const isPC2 = navigator.platform.includes('Win') || navigator.platform.includes('Mac')
const windowWidth = ref(window.innerWidth)


const handleResize = debounce(() => {
  if (!isMagApp && window.innerWidth !== windowWidth.value && (isPC || isPC2)) {
    windowWidth.value = window.innerWidth
    window.location.reload()
  }

}, 200);

let cancelTokenSource = axios.CancelToken.source();
let intervalId = null;

const heartBeat = async () => {
  cancelTokenSource = axios.CancelToken.source();

  try {
    const response = await axios.get('https://www.badmintoncn.com/cbo_call.php?action=heartBeat', {
      cancelToken: cancelTokenSource.token,
    });

    console.log('heartBeat 响应:', response.data);
  } catch (err) {
    if (!axios.isCancel(err)) {
      console.error('heartBeat 请求出错:', err);
    }
  }
};


onMounted(() => {
  heartBeat();
  intervalId = setInterval(heartBeat, 60000);
  window.addEventListener('resize', handleResize);
  document.body.style.overflow = "hidden"
  document.body.style.touchAction = "none"
})

onUnmounted(() => {
  if (intervalId) clearInterval(intervalId);
  cancelTokenSource.cancel('页面卸载，取消请求')
  window.removeEventListener('resize', handleResize)
  document.body.style.overflow = ""
  document.body.style.touchAction = ""
})


</script>

<template>
  <div id="app">
    <TitleTips v-if="!isMagApp"/>
    <RouterView/>
  </div>
</template>

<style scoped>
</style>
