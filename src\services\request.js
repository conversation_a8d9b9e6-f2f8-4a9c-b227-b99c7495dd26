// 导入axios
import axios from 'axios'
import router from "@/router/index.js";
import {jumpLogin, otherLogin} from "@/utils/common.js";
import { useAuthStore } from "@/stores/auth.js";
import {RequestController} from "@/services/requestController.js"
const requestController  = new RequestController()
const http = axios.create({
    //正式环境
    // baseURL: 'https://go.badmintoncn.com/api',
    // 测试环境
    baseURL: 'https://cbo.badmintoncn.com/api',
    // baseURL: '/api',
    timeout: 60000,
    // 跨域时候允许携带凭证
    // withCredentials: true,
    headers: {}
})

http.interceptors.request.use(
    config => {
        const authStore = useAuthStore();
        if (config.url === '/ea/login') {
            // return config;
            const { Authorization, ...restHeaders } = config.headers || {};
            return {
                ...config,
                headers: restHeaders
            };
        } else {
            config.headers = {
                Authorization: authStore.token,
                ...config.headers,
            };
            return config;
        }
    },
    error => {
        return Promise.reject(error);
    }
);



http.interceptors.response.use(
    async response => {
        const {status, data, config} = response;
        // 处理 HTTP 状态码
        if (status < 200 || status >= 300) {
            console.log('response', response)
            // debugger
            return handleHttpError(status);
        }


        // 处理业务状态码
        if (data.code !== 200) {
            console.log('response', response)
            // debugger
            // alert(data.code)
            // debugger
            return handleBusinessError(data);
        }

        // 添加 Cache-Control 头
        if (response.headers['cache-control']) {
            response.headers['cache-control'] = 'no-cache';
        }

        return data;
    },
    async error => {
        // alert('接口报错')
        // alert(error)

        const { config, status} = error



        if (status == 401) {
            console.log('error ===>', error)

            // token过期，先暂停请求
            requestController.pausePending(config)

            // 执行刷新token
            const authStore = useAuthStore();
            authStore.token = ''
            authStore.userInfo = {}
            await otherLogin()
            // 更新token后恢复请求
            config.headers.Authorization = authStore.token
            const next = await requestController.resumePending(config)
            // 返回恢复后的数据
            return next.data
        }

        return handleNetworkError(error);
    }
);

// HTTP 错误处理
const handleHttpError = (status) => {
    if(status === 400) {
        alert('请退出重新登录')
        // jumpLogin();
        return Promise.reject(new Error('未登录'));
    }
    return Promise.reject(new Error(`HTTP Error ${status}`));
}

// 业务错误处理
const handleBusinessError = (data) => {
    // debugger
    if(data.code === 400) {
        alert(data.message)
        // jumpLogin();
        return Promise.reject(new Error('需要重新登录'));
    }

    const error = new Error(data.message || 'Business Error');
    error.code = data.code;
    return Promise.reject(error);
}

// 网络错误处理
const handleNetworkError = (error) => {
    // debugger
    if(error.response?.status === 400) {
        alert('请退出重新登录')
        // jumpLogin();
        return Promise.reject(new Error('需要重新登录'));
    }

    if (error.message.includes('Network Error')) {
        error.message = '网络连接异常，请检查网络设置';
    }
    return Promise.reject(error);
}


const request = async (config) => {
    try {
        config = config.method === 'POST' ? {...config, data: config.data || {}} : config
        const data = await http.request(config)
        // alert(`请求成功${data.code}`); // 成功后的alert
        return data
    } catch (error) {
        if (request.throttle) {
            console.log('请求过于频繁，请稍后再试');
            return Promise.reject(new Error('请求过于频繁，请稍后再试'));
        }
        request.throttle = true;
        if(error.code === 'ERR_NETWORK') {
            const authStore = useAuthStore();
            if (authStore.phone) {
                const apiUrl = authStore.apiUrl
                window.mag.newWin(`${apiUrl}/upgrade?mag_hide_progress=1`);
                // window.mag.closeWin();
                // return
            } else {
                router.push({
                    name: 'upgrade'
                })
            }
        } else {
            return error
        }
    } finally {
        // 3秒后重置节流标记
        setTimeout(() => {
            request.throttle = false;
        }, 5000);
    }
}

// const request = async (config) => {
//     try {
//         const response = await http.request(config);
//         return response.data;
//     } catch (error) {
//         const normalizedError = {
//             code: error.code || 'UNKNOWN',
//             message: error.message || '未知错误',
//             isNetworkError: !error.response
//         };
//         throw normalizedError;
//     }
// }

const get = (config) => {
    return request({...config, method: 'GET'})
}
const delete1 = (config) => {
    return request({...config, method: 'DELETE'})
}

// const post = (config) => {
//     return request({
//         ...config,
//         method: 'POST',
//         header: { 'Content-Type': 'application/x-www-form-urlencoded', ...config.header },
//     })
// }
const post = (config) => {
    const transformDataToFormData = (data) => {
        if (data instanceof FormData) return data;
        const formData = new FormData();
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                formData.append(key, data[key]);
            }
        }
        return formData;
    };

    config = {
        ...config,
        method: 'POST',
        headers: {'Content-Type': 'multipart/form-data', ...config.headers},
        data: transformDataToFormData(config.data || {})
    };

    return request(config);
};

const put = (config) => {
    return request({
        ...config,
        method: 'PUT',
        header: {'Content-Type': 'application/x-www-form-urlencoded', ...config.header},
    })
}

const upload = (config) => {
    return request({
        ...config,
        method: 'PUT',
        header: {'Content-Type': 'multipart/form-data', ...config.header},
    })
}

export {get, post, put, upload, delete1}
