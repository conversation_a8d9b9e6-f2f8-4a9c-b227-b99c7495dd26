<template>
  <div class="content-grid-list" v-if="tableList.length">
    <div class="table-list-container">
      <template v-for="(val,index) in tableList">
        <div class="item-box" @click="handleDetails(val)">
          <div class="title-box">
            <div v-if="val?.recommend" class="title-tag">推荐</div>
            <div class="item-title">{{ val.name }}</div>
          </div>
          <div class="pic-content">
            <div class="image-container" v-for="(item, imgIndex) in val?.picture.slice(0,3)" :key="imgIndex">
              <van-image class="image-box" fit="cover" lazy-load :src="`${item}?imageView2/1/w/200/h/200/q/75/format/webp`" />
              <img v-if="imgIndex === val?.picture?.length-1 && val.appraisal !== null"
                   class="verified-icon" :src="verifiedIcon">
            </div>
            <div class="image-top" v-if="val.real + val.fake !== 0 && !userMes?.train">
              <div class="top-people" :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                <img class="jd-icon" :src="shieldIcon"/>
                <div class="pr-4">{{ val.real + val.fake }}</div>
              </div>
              <div class="top-ratio" v-if="val.real + val.fake >= 2"
                   :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                {{ getDominantPercentage(val.real, val.fake) }}
                <span class="per-text">%</span>
              </div>
            </div>
            <div v-if="val?.train" class="image-top-right">训</div>
            <img v-if="val?.red" class="image-top-red-packet" :src="redPacket"/>
          </div>
          <div class="desc-box" :class="{'mb-20': index === 2}">
            <div class="flex-vhc">
              <div class="user-name">{{ val?.user?.name }}</div>
              <div>{{ val?.createTime }}</div>
              <!--              <div>{{ `${val?.read}阅读` }}</div>-->
            </div>
            <div class="desc-right">
              <div>{{ `${val.brand} ${val.cate}` }}</div>
              <div class="read-box">
                <img class="eye-icon" :src="eyeIcon"/>
                <div>{{ val.read }}</div>
              </div>
            </div>
          </div>
          <div :class="{'divider-line': index !== 2 || index !== 5}"></div>
        </div>
        <Expert class="mt-15 mb-15" v-if="index === 2 && listType === 'identify' && ardentList.length" title="首鉴先锋" moreShow
                :rankingType="rankingType" :list="ardentList"/>
        <EquipRanking class="mt-15 mb-15" v-if="index === 5 && listType === 'identify' && equipList.length" :equipList="equipList"/>
      </template>
    </div>
  </div>
</template>

<script setup>
import {onMounted, ref, watch} from "vue";
import Expert from "@/views/identify/components/expert.vue";
import redPacket from "@/assets/images/reward/redPacket.gif"
import EquipRanking from "@/views/identify/components/equipRanking.vue";
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import verifiedIcon from "@/assets/images/identify/verifiedIcon.png"
import eyeIcon from "@/assets/images/common/eyeIcon.png"
import {useAuthStore} from "@/stores/auth.js";
import {getCache} from "@/utils/cache.js";
import identifyApi from "@/services/identify.js";
import {useRouter} from "vue-router";

const authStore = useAuthStore();
const router = useRouter()

const props = defineProps({
  listType: {
    type: String,
    default: 'identify'
  },
  tableList: {
    type: Array,
    default: () => []
  },
  equipList: {
    type: Array,
    default: () => []
  },
})


const userMes = ref()
const rankingType = ref(1)
const ardentList = ref([])
const expectList = ref([])

watch(() => props.tableList,
    () => {
      userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
    })


const handleDetails = (val) => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/details/index?&mag_hide_progress=1`, {
      id: val.tid,
      partition: val.partition
    });
  } else {
    router.push({
      name: 'details',
      query: {
        id: val.tid,
        partition: val.partition
      }
    })
  }
}

// 获取背景颜色
const getBackgroundColor = (real, fake) => {
  if (real + fake === 0) return '';
  if (real > fake) {
    return '#00B578';
  } else if (fake > real) {
    return '#FA5151';
  } else {
    return '#676f6e';
  }
};

// 获取主导方的百分比
const getDominantPercentage = (real, fake) => {
  const total = real + fake;
  if (total === 0) return ''; // 如果总和为 0，返回空字符串
  if (real > fake) {
    return `${Math.round((real / total) * 100)}`; // 看正的比率
  } else if (fake > real) {
    return `${Math.round((fake / total) * 100)}`; // 看假的比率
  } else {
    return '50'; // 如果相等，返回空字符串
  }
};


const fetchRanking = async (type) => {
  rankingType.value = type
  const response = await identifyApi.getRanking({
    type,
    page: 1,
    pageSize: 10,
  });
  return response; // 返回响应数据
};

const getArdentList = async () => {
  try {
    let res = await fetchRanking(1);
    if (res.code === 200 && res.data?.list?.length) {
      ardentList.value = res.data.list.splice(0, 4)
    }
    // let res2 = await fetchRanking(2);
    // if (res2.code === 200 && res2.data?.list?.length) {
    //   ardentList.value = res2.data.list.splice(0, 4); // 成功获取数据
    // }
  } catch (e) {
  }
};

const getExpectList = async () => {
  let res = await identifyApi.getRanking({
    type: 3,
    page: 1,
    pageSize: 5
  })
  if (res.code === 200) {
    expectList.value = res.data.list || []
  }
}

const picList = [
  'https://q3.itc.cn/q_70/images03/20250507/3c137955e1524078bfa78ca998825567.jpeg',
  // 'https://img0.baidu.com/it/u=1277822008,1299141917&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1732',
  'https://img0.baidu.com/it/u=655562014,3253300126&fm=253&fmt=auto&app=138&f=JPEG?w=1422&h=800',
]


onMounted(() => {
  // getExpectList()
  getArdentList()
})

</script>

<style lang="scss" scoped>
.content-grid-list {
  .table-list-container {
    .item-box {
      //margin-bottom: 20px;

      .title-box {
        display: flex;
        align-items: center;

        .item-title {
          color: #3D3D3D;
          font-size: 16px;
          font-weight: 400;
          line-height: 1.2;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .pic-content {
        margin-top: 12px;
        height: 115px;
        overflow: hidden;
        position: relative;
        border-radius: 5px;
        display: flex;
        gap: 3px;

        .image-container {
          position: relative;
          flex: 1;
          height: 115px;

          .image-box {
            width: 100%;
            height: 115px;
            //object-fit: cover;
            //object-fit:  scale-down;
          }
        }


        .image-top-right {
          position: absolute;
          top: 0;
          right: 0;
          color: #FFFFFF;
          font-size: 13px;
          padding: 3px;
          background-color: #9673FF;
          border-radius: 0 5px 0 5px;
        }

        .image-top-red-packet {
          position: absolute;
          top: 0;
          right: 0;
          padding: 3px;
          width: 22px;
          height: 22px;
        }

        .verified-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 79px;
          height: 79px;
          z-index: 1;
          opacity: 0.8;
          border-radius: 50%;
        }
      }

      .desc-box {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        color: #999999;
        font-size: 11px;
        font-weight: 400;

        .user-name {
          margin-right: 8px;
          color: #666666;
          font-size: 13px;
        }

        .desc-right {
          display: flex;
          align-items: center;

          .read-box {
            margin-left: 10px;
            display: flex;
            align-items: center;
            color: #999999;
            font-size: 10px;
            font-weight: 400;

            .eye-icon {
              width: 16px;
              height: 16px;
              margin-right: 2px;
            }
          }
        }
      }

      .divider-line {
        height: 1px;
        margin: 10px 0;
        background-color: #F5F5F5;
      }
    }
  }
}

</style>