import {useAuthStore} from "@/stores/auth.js";
import identifyApi from "@/services/identify.js";
import {getCache, setCache, setCookie} from "@/utils/cache.js";
import commonApi from "@/services/common.js";
import exp from "constants";
import router from '@/router/index.js'
import {computed} from "vue";

const authStore = useAuthStore()


const userInfo = computed(() => {
    return authStore.userInfo
})

/**
 * 格式化时间
 * @returns {any} - "2025-03-20 15:23:12"格式化为2025.03.20 15:23
 */
export const formatDate = (inputDateStr) => {
    // 创建Date对象
    const date = new Date(inputDateStr.replace(' ', 'T'));

    // 获取年、月、日、小时和分钟
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // getMonth返回0-11，所以加1
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    // 返回格式化的日期时间字符串
    return `${year}.${month}.${day} ${hours}:${minutes}`;
};


//处理数字超过1w的情况
export const formatWan = (num) => {
    if (typeof num !== 'number' || isNaN(num)) {
        return '0';
    }
    if (num >= 10000) {
        return (num / 10000).toFixed(1).replace(/\.0$/, '') + 'W';
    } else {
        return num.toString();
    }
}

export const formatDateTime = (dateTimeString) => {
    // 检查输入是否为有效字符串
    if (typeof dateTimeString !== 'string') {
        throw new Error('Input must be a string');
    }

    // 使用正则表达式提取年月日和时分部分
    const match = dateTimeString.match(/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):\d{2}$/);

    if (!match) {
        throw new Error('Invalid date time format. Expected format: YYYY-MM-DD HH:mm:ss');
    }

    // 重组为所需格式：YYYY-MM-DD HH:mm
    const [, year, month, day, hour, minute] = match;
    return `${year}.${month}.${day} ${hour}:${minute}`;
}

export const isIOS = () => {
    let userAgent = navigator.userAgent
    return /(iPhone|iPad|iPod|iOS)/i.test(userAgent)
}

export const judgeBrand = (sUserAgent) => {
    let isIphone = sUserAgent.match(/iphone/i) == "iphone";
    let isHuawei = sUserAgent.match(/huawei/i) == "huawei";
    let isHonor = sUserAgent.match(/honor/i) == "honor";
    let isOppo = sUserAgent.match(/oppo/i) == "oppo";
    let isOppoR15 = sUserAgent.match(/pacm00/i) == "pacm00";
    let isVivo = sUserAgent.match(/vivo/i) == "vivo";
    let isXiaomi = sUserAgent.match(/mi\s/i) == "mi ";
    let isXiaomi2s = sUserAgent.match(/mix\s/i) == "mix ";
    let isRedmi = sUserAgent.match(/redmi/i) == "redmi";
    let isSamsung = sUserAgent.match(/sm-/i) == "sm-";

    if (isIphone) {
        return 'iphone';
    } else if (isHuawei || isHonor) {
        return 'huawei';
    } else if (isOppo || isOppoR15) {
        return 'oppo';
    } else if (isVivo) {
        return 'vivo';
    } else if (isXiaomi || isRedmi || isXiaomi2s) {
        return 'xiaomi';
    } else if (isSamsung) {
        return 'samsung';
    } else {
        return 'android';
    }
}

export const detectDevice = () => {
    const agent = navigator.userAgent.toLowerCase();
    if (agent.includes('windows nt')) {
        // return 'win_pc'
        return 1
    } else if (agent.includes('windows phone')) {
        // return 'iphone'
        return 2
    } else if (agent.includes('iphone')) {
        // return 'iphone'
        return 2
    } else if (agent.includes('ipad')) {
        // return 'ipad'
        return 3
    } else if (agent.includes('android') && !agent.includes('arkweb')) {
        // return '安卓'
        return 4
    } else if (agent.includes('mac os')) {
        // return 'ios_pc'
        return 6
    } else if (agent.includes('linux')) {
        // return 'linux'
        return 7
    } else if (agent.includes('harmony') && agent.includes('arkweb') && agent.includes('mobile')) {
        // return '鸿蒙移动'
        return 8
    } else if (agent.includes('harmony') && agent.includes('arkweb') && !agent.includes('mobile')) {
        // return '鸿蒙pc'
        return 9
    } else {
        return 0
    }
}


export const isAndroid = () => {
    let userAgent = navigator.userAgent.toLowerCase();
    return userAgent.indexOf("android") > -1;
}


//获取路径传参
// export const getQueryParam = (name) => {
//     const regex = new RegExp(`[?&]${name}=([^&#]*)`);
//     const match = window.location.search.match(regex);
//     return match ? decodeURIComponent(match[1]) : null;
// }

export const getQueryParam = (name) => {
    // 获取完整哈希部分（包含路径和参数）
    const hash = window.location.hash;

    // 分离哈希路径和查询参数
    const [path, queryString] = hash.split('?');

    // 如果没有查询参数直接返回
    if (!queryString) return null;

    // 处理多余的分隔符（兼容 && 错误写法）
    const cleanQuery = queryString.replace(/&&+/g, '&');

    // 使用 URLSearchParams 解析
    const params = new URLSearchParams(cleanQuery);
    return params.get(name);
};

// export const getQueryParam = (name) => {
//     // 获取当前页面的完整 URL（包含查询参数）
//     const url = window.location.href;
//
//     // 创建 URL 对象（自动解析查询参数）
//     const urlObj = new URL(url);
//
//     // 使用 URLSearchParams 获取参数
//     return urlObj.searchParams.get(name);
// };


//判断是否在马甲app内
export const isMagApp = () => {
    return window.navigator.appVersion.toLowerCase().indexOf('magappx') != -1
}

export const jumpUser = (id) => {
    const {token, phone, apiUrl} = authStore;
    const {id: currentUserId} = userInfo.value;
    if (!token) {
        if (phone) {
            window.mag.newWin(`${window.location.href}?needlogin=1`);
        } else {
            jumpLogin()
        }
    }

    const navigateToMyAppraisal = () => {
        if (phone) {
            window.mag.newWin(`${apiUrl}/myAppraisal/index?mag_hide_progress=1`, {userId: id});
        } else {
            router.push({
                name: 'myAppraisal',
                query: {userId: id},
            });
        }
    };

    const navigateToHeAppraisal = () => {
        if (phone) {
            window.mag.newWin(`${apiUrl}/heAppraisal/index?mag_hide_progress=1`, {userId: id});
        } else {
            router.push({
                name: 'heAppraisal',
                query: {userId: id},
            });
        }
    };

    if (id) {
        if (currentUserId === id) {
            navigateToMyAppraisal();
        } else {
            navigateToHeAppraisal();
        }
    } else {
        // Handle the case when id is not provided
        // window.mag.newWin(`magapp://friendTimeline?needlogin=1`);
        window.mag.newWin(`${window.location.href}?needlogin=1`);
        // window.mag.newWin(`${window.location.href}`);
    }
};


//是否缓存用户组
export const isUserGroup = async () => {
    if (getCache('USER_GROUP_LIST')) return
    let res = await commonApi.getUserGroupList()
    if (res.code === 200) {
        setCache("USER_GROUP_LIST", JSON.stringify(res.data.list))
    }
}


//判断在什么环境内
export const environment = () => {
    const userAgent = navigator.userAgent || ''; // 获取 User-Agent
    if (userAgent.indexOf('cboapp') !== -1) {
        return 'magApp'
    } else if (userAgent.indexOf('MicroMessenger') !== -1) {
        return 'wxapp'
    } else {
        // 其他环境
        return 'other'
    }
}

export const extractCboAuth = (cookieString) => {
    // 使用分号和空格分割cookie字符串
    const cookies = cookieString.split('; ');
    // 遍历cookie数组
    for (let i = 0; i < cookies.length; i++) {
        // 分割每个cookie的名称和值
        const cookiePair = cookies[i].split('=');
        // 检查cookie名称是否为cbo_auth
        if (cookiePair[0] === 'cbo_auth') {
            // 返回cbo_auth的值
            return cookiePair[1];
        }
    }
    // 如果没有找到cbo_auth，返回null
    return null;
}


export const otherLogin = async () => {
    // alert('otherLogin')
    const userAgent = navigator.userAgent || ''; // 获取 User-Agent
    if (userAgent.indexOf('cboapp') !== -1) {
        window.mag.toLogin(function (rs) {
            console.log('rs', rs)
            if (rs.token && rs.user_id) {
                getUserInfo(rs.token)
                return
            }
        });
    }
    //目前网页端和微信的登录暂时不做处理
    if (userAgent.indexOf('MicroMessenger') !== -1) {
        let newUrl = window.location.href
        const tokenStartIndex = newUrl.indexOf('magapp_token=') + 'magapp_token='.length;
        const userNameStartIndex = newUrl.indexOf('user_name=');
        if (tokenStartIndex !== -1 && userNameStartIndex !== -1 && tokenStartIndex < userNameStartIndex) {
            const token = newUrl.substring(tokenStartIndex, userNameStartIndex);
            // alert(token)
            await getUserInfo(token)
            return
        } else {
            console.log("未找到用户token");
        }
    }
    if (document.cookie) {
        let cboAuthValue = extractCboAuth(document.cookie)
        if (cboAuthValue) {
            getUserInfo(cboAuthValue)
            return
        }
    }
}

export const getUserInfo = async (token) => {
    setCookie('3df5d0f298d8c119af2e389a3f456560', token)
    const userAgent = navigator.userAgent || ''; // 获取 User-Agent
    let res = await identifyApi.tokenLogin({
        token: token,
        from: userAgent.indexOf('cboapp') !== -1 ? 1 : userAgent.indexOf('MicroMessenger') !== -1 ? 2 : 3
    })
    if (res.code === 200) {
        let data = res.data.list
        authStore.token = data.jwtToken.accessToken
        authStore.userInfo = {
            id: data.uid,
            name: data.name,
            sex: data.sex,
            avatar: data.avatar,
            groupId: data.groupId,
        }
    } else {
        alert('登录失败')
    }
}

// function login(){
//
//     <!--{if strpos($_SERVER['HTTP_USER_AGENT'], 'cboapp')}-->
//     mag.toLogin(function(rs){
//     });
//     <!--{elseif strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger')}-->
//     //微信内
//     location='{CT_APP_WAP_DOMAIN}/mag/user/v1/wxwap/toWeiXinAuthorLogin?return_url={CT_HOME_URL}'+encodeURIComponent('{$_SERVER[REQUEST_URI]}');
//     <!--{else}-->
//     var r = encodeURIComponent('{CT_HOME_URL}{$_SERVER[REQUEST_URI]}');//多加一次编码，防止论坛那边无法转跳
//     location="{CT_LOGIN_URL}&referer="+encodeURIComponent('{CT_APP_BBS_DOMAIN}/cbo_referer.php?r='+r);
//     <!--{/if}-->
// }

export const jumpLogin = () => {
    const userAgent = navigator.userAgent || ''; // 获取 User-Agent
    if (userAgent.indexOf('cboapp') !== -1) {
        // 如果是 cboapp 环境
        if (!authStore.token) {
            window.mag.toLogin(function (rs) {
                getUserInfo(rs.token)
            });
        }
    } else if (userAgent.indexOf('MicroMessenger') !== -1) {
        const returnUrl = encodeURIComponent(window.location.href); // 获取当前页面 URL 并编码
        const wxLoginUrl = `${authStore.CT_APP_WAP_DOMAIN}/mag/user/v1/wxwap/toWeiXinAuthorLogin?return_url=${returnUrl}`;
        window.location.href = wxLoginUrl; // 跳转到微信登录页面
    } else {
        // 其他环境
        const currentUrl = `${authStore.CT_HOME_URL}${window.location.pathname}?/${window.location.hash}`; // 当前完整路径并编码
        const loginUrl = `${authStore.CT_LOGIN_URL}&referer=${encodeURIComponent(
            `${authStore.CT_APP_BBS_DOMAIN}/cbo_referer.php?r=${currentUrl}`
        )}`;
        window.location.href = loginUrl; // 跳转到登录页面
    }
}