<template>
  <div class="content-table-list" v-if="tableList.length">
    <div class="table-list-container">
      <template v-for="(val,index) in tableList">
        <div class="table-list-item" @click="handleDetails(val)">
          <div class="image-bg">
<!--            <van-image v-if="val.picture.length" class="image-box" fit="cover" :src="`${val.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`">-->
<!--              <template v-slot:loading>-->
<!--                <van-loading type="spinner" size="20" />-->
<!--              </template>-->
<!--            </van-image> -->
            <van-image v-if="val.picture.length" class="image-box" fit="cover" lazy-load
                       :src="`${val.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`" />
            <img v-if="!val.picture.length" class="image-box" :src="hollowIcon"/>
            <div class="image-top" v-if="val.real + val.fake !== 0 && !userMes?.train">
              <div class="top-people" :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                <img class="jd-icon" :src="shieldIcon"/>
                <div class="pr-4">{{ val.real + val.fake }}</div>
              </div>
              <div class="top-ratio" v-if="val.real + val.fake >= 2"
                   :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                {{ getDominantPercentage(val.real, val.fake) }}
                <span class="per-text">%</span>
              </div>
            </div>
            <div v-if="val?.train" class="image-top-right">训</div>
            <img v-if="val?.red" class="image-top-red-packet" :src="redPacket"/>
            <div class="background-layer"></div>
            <img v-if="val.appraisal !== null" class="verified-icon" :src="verifiedIcon"/>
            <div class="image-bot">
              <div class="flex-vc">
                <img v-if="val.brand === '其他'" class="bot-brand" :src="brandOther">
                <img v-else class="bot-brand" :src="val.brandLogo"/>
                <div class="bot-text">{{ `${val.cate}` }}</div>
              </div>
              <div class="bot-time">{{ val?.createTime }}</div>
            </div>
          </div>

          <div class="title-box">
            <div v-if="val?.recommend" class="title-tag">推荐</div>
            <div class="item-title">{{ val.name }}</div>
          </div>
          <div class="user-box">
            <div class="user-left">
<!--              <img class="user-avatar" :src="val?.user?.avatar"/>-->
              <van-image class="user-avatar"
                         lazy-load
                         :src="val?.user?.avatar" />
              <div class="user-name">{{ val?.user?.name }}</div>
            </div>
            <div class="user-right">
              <img class="eye-icon" :src="eyeIcon"/>
              <div>{{ val.read }}</div>
            </div>
          </div>
        </div>
        <!--          <Expert v-if="index === 5 && listType === 'identify'" title="鉴定达人" :list="expectList"/>-->
        <!--          <Expert v-if="index === 11 && listType === 'identify' && ardentList.length" title="热心鉴定球友" moreShow :rankingType="rankingType" :list="ardentList"/>-->
        <Expert v-if="index === 5 && listType === 'identify' && ardentList.length" title="首鉴先锋" moreShow
                :rankingType="rankingType" :list="ardentList"/>
        <EquipRanking v-if="index === 11 && listType === 'identify' && equipList.length" :equipList="equipList"/>
      </template>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import Expert from "@/views/identify/components/expert.vue";
import EquipRanking from "@/views/identify/components/equipRanking.vue";
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import redPacket from "@/assets/images/reward/redPacket.gif"
import verifiedIcon from "@/assets/images/identify/verifiedIcon.png"
import avatarIcon from "@/assets/images/identify/avatarIcon.png"
import eyeIcon from "@/assets/images/common/eyeIcon.png"
import brandOther from "@/assets/images/details/brandOther.png"
import hollowIcon from "@/assets/images/common/hollowIcon.png"
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();
import {useRouter} from "vue-router";
import identifyApi from "@/services/identify.js";
import {getCache} from "@/utils/cache.js";

const router = useRouter()


const userInfo = computed(() => {
  return authStore.userInfo
})

const userMes = ref()
const rankingType = ref(1)


const props = defineProps({
  listType: {
    type: String,
    default: 'identify'
  },
  tableList: {
    type: Array,
    default: () => []
  },
  equipList: {
    type: Array,
    default: () => []
  },
})


watch(() => props.tableList,
    () => {
      userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
    })

const expectList = ref([])

const ardentList = ref([])




const handleDetails = (val) => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/details/index?mag_hide_progress=1`, {
      id: val.tid,
      partition: val.partition
    });
  } else {
    router.push({
      name: 'details',
      query: {
        id: val.tid,
        partition: val.partition
      }
    })
  }
}


// 获取背景颜色
const getBackgroundColor = (real, fake) => {
  if (real + fake === 0) return '';
  if (real > fake) {
    return '#00B578';
  } else if (fake > real) {
    return '#FA5151';
  } else {
    return '#676f6e';
  }
};

// 获取主导方的百分比
const getDominantPercentage = (real, fake) => {
  const total = real + fake;
  if (total === 0) return ''; // 如果总和为 0，返回空字符串
  if (real > fake) {
    return `${Math.round((real / total) * 100)}`; // 看正的比率
  } else if (fake > real) {
    return `${Math.round((fake / total) * 100)}`; // 看假的比率
  } else {
    return '50'; // 如果相等，返回空字符串
  }
};

const fetchRanking = async (type) => {
  rankingType.value = type
  const response = await identifyApi.getRanking({
    type,
    page: 1,
    pageSize: 10,
  });
  return response; // 返回响应数据
};

const getArdentList = async () => {
  try {
    let res = await fetchRanking(1);
    if (res.code === 200 && res.data?.list?.length) {
      ardentList.value = res.data.list.splice(0, 4)
    }
    // let res2 = await fetchRanking(2);
    // if (res2.code === 200 && res2.data?.list?.length) {
    //   ardentList.value = res2.data.list.splice(0, 4); // 成功获取数据
    // }
  } catch (e) {
  }
};

const getExpectList = async () => {
  let res = await identifyApi.getRanking({
    type: 3,
    page: 1,
    pageSize: 5
  })
  if (res.code === 200) {
    expectList.value = res.data.list || []
  }
}

onMounted(() => {
  // getExpectList()
  getArdentList()
})


</script>

<style scoped lang="scss">
.content-table-list {
  //height: calc(100vh - 276px);
  //height: calc(100vh - 140px);
  //overflow: auto;

  .table-list-container {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    column-gap: 9px;
    row-gap: 12px;

    .table-list-item {
      width: calc((100% - 9px) / 2);

      .image-bg {
        position: relative;
        width: 171px;
        height: 171px;
        //background-color: pink;
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 8px;

        .image-box {
          width: 171px;
          height: 171px;
          object-fit: cover;
          border-radius: 5px;
        }

        .image-top-right {
          position: absolute;
          top: 0;
          right: 0;
          color: #FFFFFF;
          font-size: 13px;
          padding: 3px;
          background-color: #9673FF;
          border-radius: 0 5px 0 5px;
        }
        .image-top-red-packet {
          position: absolute;
          top: 0;
          right: 0;
          padding: 3px;
          width: 22px;
          height: 22px;
        }

        .background-layer {
          position: absolute;
          display: flex;
          align-items: center;
          bottom: 0;
          height: 28px;
          width: 100%;
          border-radius: 0 0 5px 5px;
          background: rgba(0, 0, 0, 0.1);
          /* 关键模糊代码 */
          filter: blur(20px);
          /* 提升模糊效果 */
          transform: translateZ(0); /* 启用GPU加速 */
          -webkit-backface-visibility: hidden; /* 修复模糊边缘问题 */
        }

        .verified-icon {
          position: absolute;
          top: 46%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 110px;
          height: 110px;
          z-index: 1;
          opacity: 0.8;
          border-radius: 50%;
        }

        .image-bot {
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: space-between;
          bottom: 0;
          width: 100%;
          border-radius: 0 0 5px 5px;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          background: rgba(88, 88, 88, 0.3);

          .bot-brand {
            width: 40px;
            height: 18px;
            padding: 5px;
            border-radius: 8px;
          }

          .bot-text {
            color: #FFFFFF;
            font-weight: 600;
            font-size: 10px;
          }
          .bot-time {
            color: #FFFFFF;
            font-size: 10px;
            font-weight: 600;
            padding-right: 8px;
          }
        }
      }

      .title-box {
        display: flex;
        align-items: center;

        .item-title {
          color: #3D3D3D;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.2;
          //margin-bottom: 10px;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .user-box {
        margin: 4px 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .user-left {
          display: flex;
          align-items: center;

          .user-avatar {
            width: 24px;
            height: 24px;
            overflow: hidden;
            border-radius: 50%;
          }

          .user-name {
            color: #999999;
            font-size: 13px;
            font-weight: 400;
            padding-left: 5px;
          }
        }

        .user-right {
          display: flex;
          align-items: center;
          color: #999999;
          font-size: 10px;
          font-weight: 400;

          .eye-icon {
            width: 16px;
            height: 16px;
            margin-right: 2px;
          }
        }
      }
    }
  }
}
</style>