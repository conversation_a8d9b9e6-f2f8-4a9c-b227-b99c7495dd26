<template>
  <div class="details-page">
    <van-skeleton v-if="skeletonLoading" title :loading="skeletonLoading" :row="25"/>
    <!--    <div v-if="Number(scrollTop) > 150" class="user-box">用户名</div>-->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :head-height="200" class="content-table-list"
                      :style="{height: `${dynamicHeight}px`, paddingTop: `${titleHeight}px`}">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :disabled="disabled"
          :finished-text="finishedText"
          :error.sync="errorStatus"
          @load="onLoad"
          :immediate-check="false"
          :offset="100"
          ref="listRef"
      >
        <div class="shield-tips"
             v-if="(userInfo.groupId === 1 || userInfo.groupId === 2) && detailInfo?.display === -2">
          此内容已被系统屏蔽，仅管理可见
        </div>
        <Horizontal v-if="detailInfo?.attachList && horizontal" :item="detailInfo" @change="handleChange"
                    @backTop="handleBackTop"/>
        <Vertical v-if="detailInfo?.attachList && !horizontal" :item="detailInfo" @change="handleChange"/>
        <!--        <div @click="handleTest">点击一下{{ titleHeight }}</div>-->
        <User v-if="detailInfo" :item="detailInfo" @more="handleMoreEdit"/>
        <div style="background-color: #F5F5F5; height: 5px" class="mt-15"></div>
        <div class="identify-mes">
          <div class="brand-box">
            <img class="brand-icon" :src="detailInfo?.brandLogo"/>
            <div class="brand-name">{{ `${detailInfo?.brandName}：${detailInfo?.cateName}` }}</div>
          </div>
          <div class="flex-vc mt-7" @click="handleCopy(detailInfo?.uuid)">
            <div class="brand-number">{{ `鉴定编号：${detailInfo?.uuid}` }}</div>
            <img class="brand-copy" :src="CopyIcon"/>
          </div>
        </div>
        <div v-if="hasNonLocal" class="censor-content">{{ `温馨提示：内容中有发现敏感词汇请更正后再试` }}</div>
        <div class="detail-title">
          <div v-if="detailInfo?.recommend" class="title-tag">推荐</div>
          <van-highlight :keywords="hasNonLocal ? '' : censorContent" :source-string="detailInfo?.name"/>
        </div>
        <div class="detail-message">
          <SvInputAt v-if="detailInfo?.content" class="detail-content" v-model:html="detailInfo.content"
                     ref="svInputAtRef" :editable="false" @atLink="handleAtLink"/>
        </div>
        <div v-if="detailInfo?.eid" class="equip-box" @click="jumpEquip(detailInfo?.eid)">
          <text class="equip-name">
            {{ `# ${detailInfo?.eName}` }}
          </text>
        </div>
        <div ref="targetComponent" class="target"></div>
        <Opinion v-if="detailInfo" ref="opinionRef" :info="detailInfo" :tid="detailInfo?.id" @eaTotal="changeTotal"
                 @ranking="handleRanking" @opinion="handleOpinion" @openAttr="handleOpenAttr" :userMes="userMes"
                 @modify="handleModify" @lottery="handleLottery"/>
        <div ref="anchorObserver" class="anchor-observer"></div>
        <div class="source-text">本文来自社区球友，不代表中羽在线观点和立场。如有发现侵权图片或文字，请联系本站删除。
        </div>
        <Share v-if="detailInfo && isMagApp()" :info="detailInfo"/>
        <!--       <AdSpace />-->
        <div class="divider-line"></div>
        <RelatedEa :eid="Number(detailInfo?.eid)" :userMes="userMes" :tid="pageId" @equip-list="changeEquipList"
                   @click="jumpEquip(detailInfo?.eid)"/>
        <div v-if="detailInfo?.eid && equipList?.length" class="divider-line"></div>
        <!--          <Equipment/>-->
        <!-- 在CommentTag组件上方添加一个锚点 -->
        <div ref="commentAnchor" class="comment-anchor"></div>
        <!--        <div class="divider-line mt-0"></div>-->
        <CommentList v-if="hotDataList.length && !replyId" :info="detailInfo" :list="hotDataList" :hotFlag="true"
                     @more="handleMore" @reply="handleReply" :userMes="userMes"/>
        <div class="sticky-tabs">
          <CommentTag :list="dataList" @userChange="handleUserChange" @sort="handleSort"/>
        </div>
        <div v-if="prePage" class="pre-container" @click="handlePrePage">
          <div class="pre-box">
            <div class="pre-text">查看上一页</div>
          </div>
        </div>
        <CommentList :list="dataList" :info="detailInfo" :userMes="userMes" @more="handleMore" @reply="handleReply"/>
        <div v-if="replyId" class="all-text" @click="handleAllPost">查看全部评论</div>
      </van-list>
      <div style="height: 100px"></div>
    </van-pull-refresh>
    <div class="footer-box" style="z-index: 9">
      <Footer :btnShow="isOpinionVisible" :item="detailInfo" @pageChange="handlePageChange" @comment="handleComment"
              @focus="handleFocus" :total="total" :pageIndex="pageIndex"
              @opinion="handleOpinion"/>
    </div>
    <CommentPopup ref="commentPopupRef" :info="detailInfo" :userMes="userMes" @reset="handleReset"/>
    <MorePopup ref="morePopupRef" :info="detailInfo" :item="moreItem" @del="handleDel" @edit="handleEdit"
               @reply="handleReply" @shield="handleShield" @cancel-shield="handleShield" @recommend="handleRecommend"/>
    <TestPopup ref="testPopupRef"/>
    <AttributePopup ref="attributePopupRef" @save="handleSave"/>
    <RankingPopup ref="rankingPopupRef"/>
    <LotteryDialog ref="lotteryDialogRef" :info="detailInfo" @redClose="handleReset"/>
  </div>
  <CDialog
      style="z-index: 99999"
      ref="delDialogRef"
      title="温馨提示"
      btnLeftTxt="取消"
      btnRightTxt="确定"
      @rightClick="delConfirm"
      content="删除后将不可撤回，已获经验值将返还，确定删除求鉴？"
  />
  <CDialog
      style="z-index: 99999"
      ref="delCommentRef"
      title="温馨提示"
      btnLeftTxt="取消"
      btnRightTxt="确定"
      @rightClick="delCommentConfirm"
      content="确定删除此评论？"
  />
</template>

<script setup>
import {ref, onMounted, computed, nextTick, onUnmounted, watch} from "vue";
import {getQueryParam, isMagApp, isUserGroup, jumpUser, otherLogin, jumpLogin} from "@/utils/common.js";
import identifyApi from "@/services/identify"
import Layout from "@/components/Layout.vue";
import Horizontal from "@/views/identify/details/components/horizontal.vue";
import Vertical from "@/views/identify/details/components/vertical.vue";
import User from "@/views/identify/details/components/user.vue";
import Opinion from "@/views/identify/details/components/opinion.vue";
import Share from "@/views/identify/details/components/share.vue";
import RelatedEa from "@/views/identify/details/components/relatedEa.vue";
import AdSpace from "@/views/identify/details/components/adSpace.vue";
import Equipment from "@/views/identify/details/components/equipment.vue";
import CommentList from "@/views/identify/details/components/commentList.vue";
import CommentTag from "@/views/identify/details/components/commentTag.vue";
import Footer from "@/views/identify/details/components/footer.vue";
import CommentPopup from "@/components/c-comment-popup/index.vue";
import CDialog from "@/components/c-dialog.vue";
import MorePopup from "@/views/identify/details/components/morePopup.vue";
import RankingPopup from "@/views/identify/details/components/rankingPopup.vue";
import LotteryDialog from "@/views/identify/details/components/lotteryDialog.vue";
import AttributePopup from "@/views/identify/components/attributePopup.vue";
import TestPopup from "@/views/identify/details/components/testPopup.vue";
import CopyIcon from "@/assets/images/details/copyIcon.png"
import hollowIcon from "@/assets/images/common/hollowIcon.png"
import {useRoute} from "vue-router";
import {useRouter} from "vue-router";

const route = useRoute()
const router = useRouter()

import {useAuthStore} from "@/stores/auth.js";
import SvInputAt from "@/views/identify/release/components/sv-input-at.vue";
import {getCache, getCookie} from "@/utils/cache.js";
import {value} from "lodash/seq.js";

const authStore = useAuthStore();


const userInfo = computed(() => {
  return authStore.userInfo
})


const horizontal = computed(() => {
  return authStore.horizontal
})
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const dynamicHeight = ref(0)
const svInputAtRef = ref();
const total = ref();
const pageIndex = ref(1)
const pageId = ref();
const equipList = ref([]);
const replyId = ref();
const partition = ref();
const commentAnchor = ref(null);
const editRelease = ref(false)
const refreshing = ref(false)
const loading = ref(false)
const skeletonLoading = ref(true)
const finished = ref(false)
const disabled = ref(false)
const listRef = ref()
const detailInfo = ref()
const hasNonLocal = ref(false)
const censorContent = ref([])
const prePage = ref(false)
const finishedText = ref('')
const errorStatus = ref(false)
const order = ref('asc')
const userId = ref(0)
const list = ref([]);
const tid = ref()
const moreItem = ref()
const commentPopupRef = ref()
const morePopupRef = ref()
const rankingPopupRef = ref()
const lotteryDialogRef = ref()
const attributePopupRef = ref()
const testPopupRef = ref()
const delDialogRef = ref()
const delCommentRef = ref()
const appraisalTotal = ref(0)
const userMes = ref()
const eq = ref()
const params = ref({
  page: 1,
  pageSize: 10,
})
const dataList = ref([])
const hotDataList = ref([])
const isOpinionVisible = ref(false)
const opinionRef = ref(null)
const targetComponent = ref(null)
const display = ref(false)
const scrollTop = ref(0)
let observer = null


const handleUserChange = async (val) => {
  replyId.value = ''
  switch (val) {
    case 0:
      userId.value = 0
      break
    case 1:
      userId.value = detailInfo.value?.user?.uid
      break
    case 2:
      userId.value = userInfo.value?.id
      break
  }
  // onRefresh()
  params.value.page = 1;
  hotDataList.value = []
  dataList.value = []
  await getPageList()
}

const handleCopy = (id) => {
  copyToClipboard(id,
      () => console.log('复制成功'),
      err => console.error('复制失败:', err)
  );
}

const copyToClipboard = (text, successCallback, errorCallback) => {
  // 方案一：使用现代 Clipboard API（推荐）
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text)
        .then(() => {
          successCallback?.();
          showToast('复制成功')
        })
        .catch(err => {
          errorCallback?.(err);
          // 降级到方案二
          legacyCopy(text, successCallback, errorCallback);
        });
    return;
  }

  // 方案二：兼容旧浏览器的 execCommand 方法
  legacyCopy(text, successCallback, errorCallback);
}

const legacyCopy = (text, successCallback, errorCallback) => {
  // 创建临时文本域
  const textarea = document.createElement('textarea');
  textarea.value = text;
  textarea.style.position = 'fixed'; // 避免滚动跳转
  textarea.style.left = '-9999px';
  textarea.style.opacity = '0';
  document.body.appendChild(textarea);

  try {
    // 兼容 iOS 设备
    if (navigator.userAgent.match(/iphone|ipad|ipod/i)) {
      textarea.contentEditable = true;
      textarea.readOnly = true;
      const range = document.createRange();
      range.selectNodeContents(textarea);
      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(range);
      textarea.setSelectionRange(0, 999999);
    } else {
      textarea.select();
    }

    // 执行复制命令
    const result = document.execCommand('copy');
    if (!result) throw new Error('复制命令执行失败');

    successCallback?.();
    showToast('复制成功')
  } catch (err) {
    errorCallback?.(err);
    console.error('复制失败:', err);
    showToast('自动复制失败，请手动选择后复制')
  } finally {
    document.body.removeChild(textarea);
  }
}

const handleSave = () => {
  setTimeout(() => {
    if (getCache('USER_MESSAGE')) {
      userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
    }
    location.reload();
  }, 300)
}

const handleReset = async () => {
  // await getDetails();
  // await onRefresh();
  params.value.page = 1;
  hotDataList.value = []
  dataList.value = []
  refreshing.value = true;
  await resetData()
  await opinionRef.value.getList()
  // 等待DOM更新后滚动
  rollView()

}

const rollView = () => {
  nextTick(() => {
    if (commentAnchor.value) {
      commentAnchor.value.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start'      // 对齐到顶部
      });
    }
  });
}


const handleTest = () => {
  testPopupRef.value.open()
}

const jumpEquip = (eid) => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/related/index?mag_hide_progress=1`, {
      eid: eid,
      eq: eq.value,
    });
  } else {
    router.push({
      name: 'related',
      query: {
        eid: eid,
        eq: eq.value,
      }
    })
  }

  // if(!authStore.phone) {
  //   window.location.href = `https://www.badmintoncn.com/eqm.php?a=view&eid=${eid}`;
  // } else {
  //   window.mag.newWin(`https://www.badmintoncn.com/eqm.php?a=view&eid=${eid}&mag_hide_progress=1&mag_sapp_style=1&themecolor=30BC9D`)
  // }
}


const changeEquipList = (list) => {
  equipList.value = list
}
const handleRanking = (type) => {
  rankingPopupRef.value.open(type, detailInfo.value.id)
}

const changeTotal = (total) => {
  appraisalTotal.value = total
}

const handleAtLink = (item) => {
  jumpUser(item.id)
}

const handleSort = async (type) => {
  replyId.value = ''
  order.value = type
  // onRefresh()
  params.value.page = 1;
  hotDataList.value = []
  dataList.value = []
  await getPageList()
}

const handlePrePage = async () => {
  dataList.value = []
  await nextTick(() => {
    if (commentAnchor.value) {
      commentAnchor.value.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start'      // 对齐到顶部
      });
    }
  })
  // params.value.page = pageIndex.value
  params.value.page--
  // pageIndex.value--
  getPageList()
}

//翻页
const handlePageChange = async (page) => {
  replyId.value = ''
  disabled.value = true
  await nextTick(() => {
    if (commentAnchor.value) {
      commentAnchor.value.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start'      // 对齐到顶部
      });
    }
  })
  dataList.value = []
  prePage.value = true
  params.value.page = page
  await getPageList()
}

watch(() => params.value.page, (val) => {
  pageIndex.value = val
  if (pageIndex.value === 1) {
    prePage.value = false
  }
})

const handleComment = () => {
  nextTick(() => {
    if (commentAnchor.value) {
      commentAnchor.value.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start'      // 对齐到顶部
      });
    }
  });
}


const handleFocus = () => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  if (userMes.value?.train && detailInfo.value?.user?.uid !== userInfo.value.id && detailInfo.value?.appraisal === null) {
    showToast('表态后可评论')
    return
  }
  commentPopupRef.value.open()
  // setTimeout(() => {
  //   updateDynamicHeight()
  //   const container = document.querySelector('.content-table-list')
  //   if (container) container.scrollTop = 0
  // }, 300)
}


const handleOpenAttr = () => {
  attributePopupRef.value.show()
}

const handleLottery = () => {
  lotteryDialogRef.value.show()
}

const handleOpinion = (type) => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  commentPopupRef.value.open(type)
}


const handleModify = () => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  commentPopupRef.value.modify()
}

const handleChange = () => {
  authStore.horizontal = !authStore.horizontal
}

const handleBackTop = () => {
  const pullRefreshEl = document.querySelector('.content-table-list');
  if (pullRefreshEl) {
    pullRefreshEl.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
}

const handleMoreEdit = () => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  editRelease.value = true
  moreItem.value = detailInfo.value
  morePopupRef.value.open(1)
}

const handleDel = (type) => {
  morePopupRef.value.hide()
  if (type === 1) {
    if (!detailInfo.value.editable) {
      showToast('已有球友参与互动不可删除')
    } else {
      delDialogRef.value.show()
    }
  } else {
    delCommentRef.value.show()
  }
}
//删除帖子
const delConfirm = () => {

}
//删除评论
const delCommentConfirm = () => {

}

const onRefresh = async () => {
  // replyId.value = getQueryParam('pid') || route.query.pid
  const pid = getQueryParam('pid') || route.query.pid;
  replyId.value = pid !== '0' && pid !== 0 ? pid : '';
  params.value.page = 1;
  refreshing.value = true
  await opinionRef.value.getList()
  await getDetails()
  await nextTick(() => {
    getPageList();
  })
}

const onLoad = () => {
  if (dataList.value) {
    if (params.value.page < Math.ceil(total.value / 10)) {
      params.value.page++
      nextTick(() => {
        getPageList();
      })
    } else {
      loading.value = false
    }
  }
};

const handleAllPost = async () => {
  replyId.value = ''
  params.value.page = 1;
  dataList.value = []
  await nextTick(() => {
    getPageList();
  })
}

const getPostList = async () => {
  let res = await identifyApi.getPostDetail({
    partition: partition.value,
    id: replyId.value
  })
  if (res.code === 200) {
    dataList.value = [res.data.list] || []
  }
  loading.value = false
  refreshing.value = false;
  finishedText.value = ''
}

const getPageList = () => {
  if (replyId.value) {
    getPostList()
    return
  }
  let paramsCopy = {
    ...params.value,
    fromUid: userId.value,
    order: order.value,
    uid: userInfo.value.id,
    tid: pageId.value,
  }
  loading.value = true;
  identifyApi.getComment(paramsCopy).then((res) => {
    if (res.code === 200) {
      const data = res.data || {};
      const currentData = data.list || [];
      hotDataList.value = data?.hot || [];
      if (refreshing.value) {
        dataList.value = [];
        refreshing.value = false;
      }
      dataList.value.push(...currentData);
      if (currentData.length < params.value.pageSize) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = '没有更多了'
      if (dataList.value.length === 0) {
        finishedText.value = ''
      }
    } else {
      errorStatus.value = true;
    }
  }).finally(() => {
    loading.value = false
    refreshing.value = false;
    nextTick(() => {
      disabled.value = false
    })
  });
}

const getDetails = async () => {
  skeletonLoading.value = true
  let res = await identifyApi.getEaDetails({id: pageId.value, uid: userInfo.value.id, partition: partition.value})
  const {code, data} = res
  if (code === 200) {
    detailInfo.value = data.list
    total.value = data.list.posts
    if (detailInfo.value?.censorContent?.length) {
      detailInfo.value.censorContent.some((res) => {
        if (res.from === 'local') {
          censorContent.value.push(res.content);
        } else {
          hasNonLocal.value = true;
          return true;
        }
        return false;
      });
    }
    setTimeout(() => {
      skeletonLoading.value = false
    }, 500)
  } else if (res.code === 4000) {
    // alert(res.message)
    if (isMagApp()) {
      window.mag.closeWin();
    } else {
      router.back()
    }
  }
}


const handleMore = (item) => {
  if (userMes.value?.train && detailInfo.value?.user?.uid !== userInfo.value.id && detailInfo.value?.appraisal === null) {
    showToast('表态后可评论')
    return
  }
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  editRelease.value = false
  moreItem.value = item
  morePopupRef.value.open(2)
}

const handleReply = (item) => {
  if (userMes.value?.train && detailInfo.value?.user?.uid !== userInfo.value.id && detailInfo.value?.appraisal === null) {
    showToast('表态后可评论')
    return
  }
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  commentPopupRef.value.restore(item)
}

const handleEdit = (item) => {
  if (editRelease.value) {
    pageLoad()
    if (authStore.phone) {
      const apiUrl = authStore.apiUrl
      window.mag.newWin(`${apiUrl}/release/index?mag_hide_progress=1`, {id: detailInfo.value.id});
    } else {
      router.push({
        name: 'release',
        query: {
          id: detailInfo.value.id
        }
      })
    }
  } else {
    commentPopupRef.value.edit(item)
  }
}


// watch(() => scrollTop.value,
//     (val) => {
//       if (Number(val) > 150) {
//         window.mag.setTitle('');
//         // window.mag.hideMore();
//         window.mag.setNavigationBarStyle({
//           navi_transparent: 1, // 透明导航栏
//           navi_transparency: 0,// 透明度为 0
//           navi_style: 0, // 白色导航栏文字
//         });
//       } else {
//         // window.mag.showMore();
//         window.mag.setTitle('详情');
//         window.mag.setNavigationBarStyle({
//           navi_transparent: 0, // 透明导航栏
//           navi_transparency: 1,// 透明度为 0
//           navi_style: 0, // 白色导航栏文字
//         });
//       }
//     })

const handleShield = async (obj) => {
  const {item, type} = obj
  let res = await identifyApi.setCensor({
    uid: userInfo.value.id,
    cate: type === 1 ? 'thread' : 'post',
    id: item.id,
    display: item.display === 0 ? -2 : 0,
  })
  if (res.code === 200) {
    if (item.display === -2) {
      showToast('已取消屏蔽')
    } else {
      showToast('已屏蔽')
    }
    await handleReset()
  }
}


const handleRecommend = async (item) => {
  let res = await identifyApi.recommendThread({
    id: item.id,
    partition: partition.value
  })
  if (res.code === 200) {
    detailInfo.value.recommend = !detailInfo.value.recommend
    // showToast('推荐帖子成功')
    if (detailInfo.value.recommend) {
      showToast('推荐帖子成功')
    } else {
      showToast('取消推荐成功')
    }
  }
}


const scrollEl = ref(null)
const targetEl = ref(null)
const scrollHandler = ref(null)

const resetData = async () => {
  params.value.page = 1;
  hotDataList.value = [];
  dataList.value = [];
  refreshing.value = true;
  pageId.value = getQueryParam('id') || route.query.id
  const pid = getQueryParam('pid') || route.query.pid;
  replyId.value = pid !== '0' && pid !== 0 ? pid : '';
  partition.value = getQueryParam('partition') || route.query.partition
  await getDetails()
  if (replyId.value) {
    rollView()
  }
  await getPageList()
  await nextTick(() => {
    if (userInfo.value.id && Number(userInfo.value.id) !== Number(detailInfo?.value?.user?.uid) && detailInfo?.value?.appraisal === null) {
      isOpinionVisible.value = true
    }
    if (userInfo.value.id && Number(userInfo.value.id) !== Number(detailInfo?.value?.user?.uid) && detailInfo?.value?.appraisal === null) {
      scrollEl.value = document.querySelector(".content-table-list")
      targetEl.value = document.querySelector(".target")
      scrollHandler.value = () => {
        isOpinionVisible.value = scrollEl.value.scrollTop + scrollEl.value.offsetHeight <=
            targetEl.value.offsetTop - scrollEl.value.offsetTop + 100
      }
      if (scrollEl.value) {
        scrollEl.value.addEventListener("scroll", scrollHandler.value)
      }
    } else {
      if (scrollEl.value && scrollHandler.value) {
        scrollEl.value.removeEventListener("scroll", scrollHandler.value);
        scrollEl.value = null;
        targetEl.value = null;
        scrollHandler.value = null;
      }
      isOpinionVisible.value = false
    }
    // scrollEl.value = document.querySelector(".content-table-list")
    // targetEl.value = document.querySelector(".target")
    // scrollHandler.value = () => {
    //   scrollTop.value = scrollEl.value.scrollTop
    // }
    // if (scrollEl.value) {
    //   scrollEl.value.addEventListener("scroll", scrollHandler.value)
    // }
  });
  if (authStore.phone) {
    window.mag.setTitle('详情');
  } else {
    document.title = '详情'
  }
  window.mag.showNavigation()
}


const pageLoad = () => {
  window.mag.setPageLife({
    pageAppear: async function () {
      params.value.page = 1;
      hotDataList.value = []
      dataList.value = []
      refreshing.value = true;
      await resetData()
    }
  });
}

const handleResize = () => {
  updateDynamicHeight()
}

const updateDynamicHeight = () => {
  if (document.getElementById('myTitle')) {
    dynamicHeight.value = window.innerHeight - 10 - document.getElementById('myTitle')?.offsetHeight || 0;
  } else {
    dynamicHeight.value = window.innerHeight - 10
  }
  window.addEventListener('resize', handleResize)
}

onMounted(async () => {
  if (getCache('USER_MESSAGE')) {
    userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
  }
  params.value.page = 1;
  hotDataList.value = []
  dataList.value = []
  refreshing.value = true;
  if (getQueryParam('eq') || route.query.eq) {
    eq.value = getQueryParam('eq') || route.query.eq;
  }
  // if (!authStore.token) {
  //   await otherLogin()
  // }
  if (authStore.phone) {
    if (getCookie('cbo_magapp_token')?.length > 1) {
      await otherLogin()
    }
  }
  await isUserGroup()
  await resetData()
  nextTick(() => {
    updateDynamicHeight()
  })
  // document.body.addEventListener('focusin', () => {
  //   console.log('键盘打开')
  // })
  // document.body.addEventListener('focusout', () => {
  //   console.log('键盘收起')
  // })
})

onUnmounted(() => {
  if (scrollEl.value && scrollHandler.value) {
    scrollEl.value.removeEventListener("scroll", scrollHandler.value)
  }
  scrollEl.value = null
  targetEl.value = null
  scrollHandler.value = null
});


</script>

<style scoped lang="scss">
.details-page {
  color: #3D3D3D;
  position: relative;
  width: 100vw;
  height: 100%;
  background-color: #FFFFFF;

  .user-box {
    color: red;
    z-index: 9999999;
    //position: fixed;
    position: sticky;
    top: 0;
    left: 0;
    height: 86px;
    background-color: #FFFFFF;
  }

  .content-table-list {
    flex: 1;
    overflow-y: auto;
    //height: 100vh;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none;
  }

  .shield-tips {
    color: #FF853E;
    font-size: 14px;
    text-align: center;
    height: 36px;
    line-height: 36px;
    background-color: #FFF4EE;
  }

  .image-box {
    width: 100%;
  }

  .identify-mes {
    //margin: 12px 0 0;
    padding: 15px 12px 8px;
    //background-color: #F5F5F5;

    .brand-box {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-weight: 600;

      .brand-icon {
        width: 40px;
        height: 18px;
        margin-right: 10px;
        border-radius: 3px;
      }
    }

    .brand-number {
      color: #666666;
      font-weight: 400;
      font-size: 13px;
    }

    .brand-copy {
      width: 20px;
      height: 20px;
      margin-left: 2px;
    }
  }

  .detail-title {
    margin: 12px;
    font-weight: 600;
    font-size: 18px;
    line-height: 1.4;
    white-space: pre-wrap;

    .title-tag {
      float: left;
      margin-right: 5px;
      margin-top: 3px;
      flex-shrink: 0;
      color: #FA5151;
      font-size: 10px;
      font-weight: 700;
      padding: 2px 3px;
      border-radius: 3px;
      border: 1px solid #FA5151;
    }

    ::v-deep {
      padding: 0 !important;
    }
  }

  .detail-message {
    margin: 0 12px;
    line-height: 1.5;
    font-size: 16px;
    font-weight: 400;
    white-space: pre-wrap;

    .detail-content {
      ::v-deep {
        font-size: 16px !important;
        padding: 0 !important;
        line-height: 1.6 !important;
      }
    }
  }

  .equip-box {
    margin: 10px 12px 0;
    color: #5070D6;
    font-size: 12px;

    .equip-name {
      padding: 5px;
      border-radius: 3px;
      background-color: #EDF1FF;
    }
  }

  .source-text {
    line-height: 1.1;
    font-weight: 400;
    color: #C1C1C1;
    font-size: 13px;
    margin: 20px 12px 14px;
  }

  .divider-line {
    height: 5px;
    margin-top: 15px;
    background-color: #F5F5F5;
  }

  .pre-container {
    padding: 15px 12px 5px;
    background-color: #FFFFFF;

    .pre-box {
      .pre-text {
        color: #3D3D3D;
        font-size: 10px;
        font-weight: 400;
        border-radius: 5px;
        padding: 6px 0;
        text-align: center;
        background-color: #F5F5F5;
      }
    }
  }
}


.footer-box {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 2;
}

::v-deep .van-highlight__tag {
  color: #FA5151 !important;
}

.censor-content {
  color: #FA5151;
  font-size: 13px;
  margin: 15px 12px 5px;
  font-weight: 400;
}

.sticky-tabs {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 1;
}

.all-text {
  padding-top: 20px;
  color: #5070D6;
  font-size: 16px;
  text-align: center;
}
</style>