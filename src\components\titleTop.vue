<template>
  <div class="title-container">
    <div class="title-left">
      <div class="piece"></div>
      <slot name="title">
        <div class="title-name">{{ title }}</div>
      </slot>
    </div>
    <slot name="right">
      <div v-if="right" class="flex-vc">
        <img class="icon-box" v-if="switchIcon" :src="switchType ? listIcon : gridIcon" @click="handleSwitch">
        <!--        <div class="screen-container">-->
        <!--          <div class="screen-tag-container">-->
        <!--            <div-->
        <!--                v-for="tag in filteredTags"-->
        <!--                :key="tag.id"-->
        <!--                :class="['screen-tag', { 'active-tag': isActive(tag.id) }]"-->
        <!--                @click="handleScreen(tag.id)"-->
        <!--            >-->
        <!--              {{ tag.name }}-->
        <!--            </div>-->
        <!--          </div>-->

        <!--        </div>-->
        <van-popover v-model:show="showPopover2" theme="dark" :actions="filteredTags" placement="bottom-end"
                     :show-arrow="false" @open="handleOpen2" @close="handleOpen2" @select="handleScreen">
          <template #reference>
            <div class="sort-container flex-vc">
              <div class="sort-text">{{ openName2 }}</div>
              <img class="sort-icon" :src="openType2 ? bottomIcon : rightIcon"/>
            </div>
          </template>
        </van-popover>
        <van-popover v-if="!userMes?.train" v-model:show="showPopover" theme="dark" :actions="selectList"
                     placement="bottom-end"
                     :show-arrow="false" @open="handleOpen" @close="handleOpen" @select="onSelect">
          <template #reference>
            <div class="sort-container flex-vc">
              <div class="sort-text">{{ openName }}</div>
              <img class="sort-icon" :src="openType ? bottomIcon : rightIcon"/>
            </div>
          </template>
        </van-popover>
      </div>
    </slot>
  </div>
  <van-overlay :show="visible" :opacity="0.9" z-index="999">
  </van-overlay>
</template>

<script setup>
import {ref, computed, watch} from "vue";
import rightIcon from "@/assets/images/common/ysj.png"
import bottomIcon from "@/assets/images/common/dsj.png"
import gridIcon from "@/assets/images/common/gridIcon.png"
import listIcon from "@/assets/images/common/listIcon.png"

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  right: {
    type: Boolean,
    default: true
  },
  state: {
    type: Number,
    default: 0
  },
  order: {
    type: Number,
    default: 1
  },
  userMes: {
    type: Object,
    default: () => {
    }
  },
  switchIcon: {
    type: Boolean,
    default: false
  },
  switchType: {
    type: Boolean,
    default: false
  },
  equipType: {
    type: Boolean,
    default: false
  }
})
const visible = ref(false)
const showPopover = ref(false);
const showPopover2 = ref(false);
const openType = ref(false)
const openType2 = ref(false)
const openName = ref('默认')
const openName2 = ref('全部')

const actions = ref([
  {text: '全部', id: 1},
  {text: '最新', id: 2},
  {text: '最热', id: 3},
]);

const selectList = ref([
  {text: '全部', id: 1},
  {text: '最新', id: 2},
  {text: '最热', id: 3},
])


const emit = defineEmits(['change', 'sort'])

const tags = [
  {text: '全部', id: 0},
  {text: '待鉴', id: 1},
  {text: '已鉴', id: 2},
  {text: '推荐', id: 4},
  {text: '争议', id: 3},
];

const equipTags = [
  {text: '全部', id: 0},
  {text: '待鉴', id: 1},
  {text: '已鉴', id: 2},
  {text: '争议', id: 3},
];

const filteredTags = computed(() => {
  if (props.userMes?.train) {
    if (props.equipType) {
      return equipTags.slice(0, 3);
    } else {
      return tags.slice(0, 3);
    }
  } else {
    if (props.equipType) {
      return equipTags;
    } else {
      return tags;
    }

  }
})

const handleSwitch = () => {
  emit('compose')
}


const handleOpen = () => {
  visible.value = !visible.value
  openType.value = !openType.value
}

const handleOpen2 = () => {
  visible.value = !visible.value
  openType2.value = !openType2.value
}

const onSelect = (action) => {
  openName.value = action.text
  emit('sort', action.id)
}

const isActive = (value) => {
  // if (value === '默认') {
  //   return ['默认', '全部'].includes(props.state);
  // }
  return props.state === value;
};


watch(() => props.state, (val) => {
  openName2.value = tags.find(tag => tag.id === val)?.text || '全部';
});

watch(() => props.order, (val) => {
  openName.value = selectList.value?.find(item => item.id === val)?.text || '全部';
}, {
   immediate: true
});


const handleScreen = (val) => {
  openName2.value = val.text
  if (val.id === 1) {
    selectList.value = actions.value.slice(0, 2)
  } else {
    selectList.value = actions.value
  }
  emit('change', val.id)
}

</script>

<style lang="scss" scoped>
.title-container {
  padding-bottom: 3px;
  position: sticky;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-left {
    display: flex;
    //align-items: center;
    color: #3D3D3D;
    font-size: 16px;

    .piece {
      width: 4px;
      height: 14px;
      margin-right: 5px;
      background: #478e87;
    }
  }

  .screen-container {
    background-color: #F5F5F5;
    border-radius: 5px;

    .screen-tag-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2px;

      .screen-tag {
        font-size: 10px;
        color: #666666;
        padding: 4px 9px;
        border-radius: 3px;
      }

      .active-tag {
        color: #3D3D3D;
        background-color: #FFFFFF;
      }
    }
  }

  .sort-container {
    border-radius: 5px;
    padding: 4px 8px;
    margin-left: 12px;
    background: #F5F5F5;

    .sort-text {
      margin-top: 1px;
      color: #3D3D3D;
      font-size: 10px;
      font-weight: 400;
    }

    .sort-icon {
      width: 14px;
      height: 14px;
      //margin-left: ;
    }
  }
}

.icon-box {
  width: 16px;
  height: 16px;
  //margin-right: 10px;
}

::v-deep .van-popover__action {
  width: 60px;
  font-size: 13px !important;
  font-weight: 400;
}

.van-overlay {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>