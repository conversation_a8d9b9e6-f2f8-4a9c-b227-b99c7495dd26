<template>
  <div class="he-appraisal">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - 10px - ${titleHeight}px)`,paddingTop: `${titleHeight}px`}">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="finishedText"
          @load="onLoad"
          v-model:error="error"
          :error.sync="errorStatus"
          error-text="请求失败，点击重新加载"
          :immediate-check="false"
          ref="listRef"
      >
        <OtherUser :userMes="userMes"/>
        <div class="sticky-tabs" @click="handleTab">
          <van-tabs class="tab-box"
                    v-model:active="activeTab"
                    @click-tab="debouncedTabChange"
                    :disabled="isTabLoading"
                    ref="tabsRef"
                    color="#478E87"
                    line-width="28"
                    line-height="2"
                    title-active-color="#478E87"
                    title-inactive-color="#3D3D3D">
            <van-tab :title="tab.name" :name="tab.id" v-for="(tab, index) of tabs" :key="index"/>
          </van-tabs>
          <div style="border: 1px solid #F5F5F5; margin-top: -1px;"></div>
        </div>
        <div v-if="activeTab !== 3" class="tags-box flex-vc">
          <div class="tags-page">
            <div
                v-for="(item, index) in categoryList"
                :key="index"
                class="tag-item"
                :class="{'active-item': category === item.id}"
                @click="toggleSelection(item)"
            >
              <div>{{ item.name }}</div>
              <div class="pt-1">{{ item.count }}</div>
            </div>
          </div>
          <img v-if="categoryList?.length" class="tag-filter" :src="filterIcon" @click="handleScreen"/>
        </div>
        <div v-if="tableList.length">
          <div v-for="(item, index) in tableList" :key="index">
            <div class="mes-content" @click="handleDetails(item)" :style="{marginTop: activeTab === 3 ? '10px' : 0}">
              <div class="picture-box">
                <van-image
                    v-if="item?.picture?.length && activeTab !== 3"
                    class="img-box"
                    fit="cover"
                    lazy-load
                    :src="`${item.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`"
                />
                <van-image
                    v-if="item?.pic && activeTab === 3"
                    class="img-box"
                    fit="cover"
                    lazy-load
                    :src="`${item.pic}?imageView2/1/w/200/h/200/q/75/format/webp`"
                />
                <img v-if="!item.picture?.length && !item?.pic" class="img-box" :src="hollowIcon"/>
                <div v-if="activeTab === 2 && item.diff" class="diff-box">
                  <div class="diff-text">异鉴</div>
                </div>
                <div v-if="item.display === -1" class="cover-box">审核中</div>
                <div v-if="item.display === -2" class="cover-box">已被屏蔽</div>
                <div class="image-top" v-if="item.real + item.fake !== 0 && !myUserMes?.train && activeTab !== 3">
                  <div class="top-people" :style="{ 'background-color': getBackgroundColor(item.real, item.fake) }">
                    <img class="jd-icon" :src="shieldIcon"/>
                    <div class="pr-4">{{ item.real + item.fake }}</div>
                  </div>
                  <div class="top-ratio" v-if="item.real + item.fake >= 2"
                       :style="{ 'background-color': getBackgroundColor(item.real, item.fake) }">
                    {{ getDominantPercentage(item.real, item.fake) }}
                    <span class="per-text">%</span>
                  </div>
                </div>
              </div>
              <div v-if="activeTab !== 3" class="mes-box">
                <div>
                  <div class="title-box">
                    <div v-if="item?.recommend" class="title-tag">推荐</div>
                    <div class="mes-title">{{ item.name }}</div>
                  </div>
                  <div class="mes-desc flex-vc">
                    <div v-if="activeTab === 2 && item.train" class="train-tag">训</div>
                    <div v-if="activeTab === 2" class="mes-tag flex-vc" :class="{'red-tag' : !item.appraisal}">
                      <img class="tag-icon" :src="shieldIcon"/>
                      <div class="mt-1">{{ !item.appraisal ? '看假' : '看正' }}</div>
                    </div>
                    <div class="mes-time">{{ activeTab === 1 ? `发布于${item.createTime}` : `${item.createTime}` }}
                    </div>
                  </div>
                </div>
                <div v-if="activeTab === 2" class="brand-left">
                  <div class="brand-name pl-0">{{ `${item.brand} | ${item.cate}` }}</div>
                </div>
                <div v-if="activeTab === 1" class="brand-box">
                  <div class="brand-left">
                    <img class="brand-icon" :src="item?.brandLogo"/>
                    <div class="brand-name">{{ `${item.brand} | ${item.cate}` }}</div>
                  </div>
                  <div class="brand-right">
                    <img class="eye-icon" :src="eyeIcon"/>
                    <div>{{ item.read }}</div>
                  </div>
                </div>
                <div v-if="activeTab === 2" class="user-box">
                  <div class="user-left">
                    <van-image
                        v-if="item?.user?.avatar"
                        class="user-avatar"
                        lazy-load
                        :src="item?.user?.avatar"
                    />
                    <div class="user-name">{{ item?.user?.name }}</div>
                  </div>
                  <div class="user-right">
                    <img class="eye-icon" :src="eyeIcon"/>
                    <div>{{ item.read }}</div>
                  </div>
                </div>
              </div>
              <div v-if="activeTab === 3" class="mes-text-box">
                <div class="item-name">{{item.name}}</div>
                <div class="item-time">{{`发布于${item.createTime}`}}</div>
              </div>
            </div>
            <div style="height: 1px; background: #F5F5F5; margin: 12px"></div>
          </div>
        </div>
        <div>
          <Loading ref="subLoadingRef" discText="加载中..." class="loading-box"/>
        </div>
        <div v-if="!tableList.length && !loading">
          <Empty class="empty-wrap"/>
        </div>
      </van-list>
    </van-pull-refresh>
    <ScreenPopup ref="ScreenPopupRef" :userMes="myUserMes" @confirm="handleConfirm"/>
  </div>

</template>

<script setup>
import {computed, ref, onMounted, nextTick} from "vue";
import {formatDate, getQueryParam, isUserGroup, otherLogin} from "@/utils/common"
import OtherUser from "@/views/identify/heAppraisal/components/otherUser.vue";
import ScreenPopup from "@/views/identify/myAppraisal/components/screenPopup.vue";
import Loading from "@/components/loading/index.vue"
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import hollowIcon from "@/assets/images/common/hollowIcon.png"
import eyeIcon from "@/assets/images/common/eyeIcon.png"
import filterIcon from "@/assets/images/identify/filterIcon.png"
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {formatDateTime} from "@/utils/common";
import {debounce} from "lodash";
import {useRoute} from "vue-router";
import router from "@/router/index.js";

const route = useRoute()
import {setCache, getCache, getCookie} from "@/utils/cache.js";


const authStore = useAuthStore();


const userInfo = computed(() => {
  return authStore.userInfo
})

const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const ScreenPopupRef = ref('')
const classify = ref('')
const refreshing = ref(false)
const loading = ref(false)
const subLoadingRef = ref()
const finished = ref(false)
const listRef = ref()
const tableList = ref([]);
const finishedText = ref('')
const error = ref(false)
const errorStatus = ref(false)
const activeTab = ref()
const userId = ref()
const category = ref(-1)
const tabHeight = ref(44)
const brand = ref()
const appraisal = ref()
const diff = ref()
const userMes = ref()  //当前查看的数据的人的信息
const myUserMes = ref()  //当前登录人的信息
const isTabLoading = ref(false)
const params = ref({
  page: 1,
  pageSize: 10,
})
const tabs = ref([
  {
    name: "求鉴",
    id: 1,
  }, {
    name: "鉴定",
    id: 2,
  }, {
    name: "好文",
    id: 3,
  },
])

const handleScreen = () => {
  ScreenPopupRef.value.show(activeTab.value)
}


const categoryList = ref([
  {
    "id": -1,
    "count": 0,
    "name": "全部",
  },
  {
    "id": 1,
    "count": 0,
    "name": "球拍",
  },
  {
    "id": 3,
    "count": 0,
    "name": "球鞋",
  },
  {
    "id": 8,
    "count": 0,
    "name": "羽毛球",
  },
  {
    "id": 7,
    "count": 0,
    "name": "球拍线",
  },
  {
    "id": 0,
    "count": 0,
    "name": "其他",
  }
]);


// 获取背景颜色
const getBackgroundColor = (real, fake) => {
  if (real + fake === 0) return '';
  if (real > fake) {
    return '#00B578';
  } else if (fake > real) {
    return '#FA5151';
  } else {
    return '#676f6e';
  }
};

// 获取主导方的百分比
const getDominantPercentage = (real, fake) => {
  const total = real + fake;
  if (total === 0) return ''; // 如果总和为 0，返回空字符串
  if (real > fake) {
    return `${Math.round((real / total) * 100)}`; // 看正的比率
  } else if (fake > real) {
    return `${Math.round((fake / total) * 100)}`; // 看假的比率
  } else {
    return '50'; // 如果相等，返回空字符串
  }
};

const handleDetails = (item) => {
  if (item.display === -2 && (![1, 2].includes(userInfo.value.groupId) || Number(userInfo.value.id) === 117229)) {
    showToast('此内容被屏蔽或正在审核中')
    return
  }
  if (activeTab.value === 2 && item.display === -1 && (![1, 2].includes(userInfo.value.groupId) || Number(userInfo.value.id) === 117229)) {
    showToast('此内容被屏蔽或正在审核中')
    return
  }
  if(activeTab.value === 3) {
    if(!authStore.phone) {
      window.location.href = `https://m2.badmintoncn.com/mag/circle/v1/forum/threadWapPage?tid=${item.tid}&themecolor=478e87&circle_id=114`;
    } else {
      window.mag.newWin(`https://m2.badmintoncn.com/mag/circle/v1/forum/threadWapPage?&mag_hide_progress=1&tid=${item.tid}&themecolor=478e87&circle_id=114`)
    }
  } else {
    if (authStore.phone) {
      const apiUrl = authStore.apiUrl
      window.mag.newWin(`${apiUrl}/details/index?mag_hide_progress=1&id=${item.tid}&partition=${item.partition}`);
      // pageLoad()
    } else {
      router.push({
        name: 'details',
        query: {
          id: item.tid,
          partition: item.partition
        }
      })
    }
  }
}

const handleTab = () => {
  tableList.value = []
}

const tabChange = async (item) => {
  if (isTabLoading.value) return
  isTabLoading.value = true
  try {
    tableList.value = []
    activeTab.value = item.name
    setCache('TAB_TAG', item.name);
    category.value = -1
    diff.value = -1
    brand.value = ''
    appraisal.value = ''
    params.value.page = 1
    ScreenPopupRef.value.reset()
    if (item.name == 3) {
      await getPageList()
    } else {
      await Promise.all([getCateCount(), getPageList()])
    }
    // await Promise.all([getCateCount(), getPageList()])
  } finally {
    isTabLoading.value = false
  }
}

const debouncedTabChange = debounce(tabChange, 300)

const toggleSelection = (item) => {
  subLoadingRef.value.show()
  category.value = item.id
  params.value.page = 1;
  tableList.value = []
  getPageList()
  getCateCount()
}


const onRefresh = () => {
  console.log("onRefresh");
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getSetting()
    getCateCount()
    getPageList();
  })
}

const onLoad = () => {
  console.log('onLoad====')
  if (tableList.value) {
    params.value.page++
    nextTick(() => {
      getPageList();
    })
  }
};

const handleConfirm = (obj) => {
  brand.value = obj.brand
  appraisal.value = obj.appraisal
  diff.value = activeTab.value === 2 ? obj.diff : -1
  tableList.value = []
  params.value.page = 1
  getPageList()
  getCateCount()
}


let abortController = null

// 修改后的 getPageList 函数
const getPageList = async () => {
  if (abortController) {
    abortController.abort();
  }
  abortController = new AbortController();
  try {
    const paramsCopy = {
      ...params.value,
      uid: userId.value,
      // uid: 117229
    };
    if (activeTab.value !== 3) {
      paramsCopy.type = activeTab.value;
      paramsCopy.cate = category.value === -1 ? null : category.value;
      paramsCopy.brand = brand.value === -1 ? null : brand.value;
      paramsCopy.appraisal = appraisal.value === -1 ? null : appraisal.value;
      paramsCopy.diff = diff.value === -1 ? null : diff.value;
    }
    loading.value = true;
    const res = activeTab.value === 3
        ? await identifyApi.getMySelected(paramsCopy, {signal: abortController.signal})
        : await identifyApi.getMyEaList(paramsCopy, {signal: abortController.signal});
    // const paramsCopy = {
    //   ...params.value,
    //   uid: userId.value,
    //   // uid: userInfo.value && userInfo.value.id ? userInfo.value.id : null,
    //   type: activeTab.value,
    //   cate: category.value === -1 ? null : category.value,
    //   brand: brand.value === -1 ? null : brand.value,
    //   appraisal: appraisal.value === -1 ? null : appraisal.value,
    //   diff: diff.value === -1 ? null : diff.value,
    // };
    // loading.value = true;
    // const res = await identifyApi.getMyEaList(paramsCopy, {
    //   signal: abortController.signal,
    // });
    if (res.code === 200) {
      const data = res.data || {};
      const dataList = data.list || [];
      if (refreshing.value) {
        tableList.value = [];
        refreshing.value = false;
      }
      tableList.value = [...tableList.value, ...dataList];
      if (dataList.length < params.value.pageSize) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = tableList.value.length ? "没有更多了" : "";
    } else {
      errorStatus.value = true;
      error.value = true;
    }
  } catch (error) {
    if (error.name !== 'AbortError') {
      errorStatus.value = true;
    }
    error.value = true;
  } finally {
    loading.value = false;
    subLoadingRef.value.hide()
    abortController = null;
  }
};

const getCateCount = async () => {
  if (activeTab.value == 3) return
  let res = await identifyApi.getThreadCount({
    uid: userId.value,
    type: activeTab.value,
    target: 'cate',
    brand: brand.value === -1 ? '' : brand.value,
    appraisal: appraisal.value === -1 ? null : appraisal.value,
    diff: diff.value === -1 ? null : diff.value,
  })
  if (res.code === 200) {
    categoryList.value = res.data.list
  }
}

const resetData = async () => {
  tableList.value = []
  await getPageList()
  await getCateCount()
  if (authStore.phone) {
    window.mag.setTitle('Ta的鉴定');
  } else {
    document.title = 'Ta的鉴定'
  }
  window.mag.showNavigation()
  window.mag.setData({
    // shareData: {
    //   title: 'Ta的鉴定',
    //   des: '装备鉴定 Ta的鉴定',
    //   picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
    //   linkurl: window.location.href,
    // }
    shareData: {
      title: `${userMes.value?.user?.name}的鉴定数据`,
      des: `近期鉴定量${userMes.value?.count}、总计鉴定量${userMes.value?.countAll}`,
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: window.location.href,
    }
  });
  // window.mag.addRefreshComponent()
}

const pageLoad = () => {
  window.mag.setPageLife({
    pageAppear: async function () {
      activeTab.value = 1
      await resetData()
    }
  });
}

const getSetting = async () => {
  let res = await identifyApi.queryUserList({
    ids: userId.value
  })
  if (res.code === 200) {
    userMes.value = res.data.list[0]
  }
}

onMounted(async () => {
  userId.value = Number(route.query.userId) || Number(getQueryParam('userId')) || 0
  getSetting()
  if (getCache('TAB_TAG')) {
    activeTab.value = Number(getCache('TAB_TAG'))
  }
  myUserMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
  nextTick(() => {
    let stickyTabsElement = document.querySelector('.sticky-tabs');
    tabHeight.value = stickyTabsElement.offsetHeight;
  });
  // window.mag.hideMore()
  subLoadingRef.value.show()
  // if (!authStore.token) {
  //   await otherLogin()
  // }
  if (authStore.phone) {
    if (getCookie('cbo_magapp_token')?.length > 1) {
      await otherLogin()
    }
  }
  await resetData()
  await isUserGroup()
})

</script>

<style scoped lang="scss">
.he-appraisal {
  position: relative;
  height: 100%;
  width: 100vw;
  background-color: #FFFFFF;

  .sticky-tabs {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .tags-page {
    flex: 1;
    margin: 10px 12px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    column-gap: 9px;

    .tag-item {
      width: calc((100% - 45px) / 5);
      color: #3D3D3D;
      font-size: 10px;
      font-weight: 400;
      text-align: center;
      padding: 5px 0;
      border-radius: 5px;
      background-color: #F5F5F5;
    }

    .active-item {
      color: #478E87;
      font-weight: 500;
      background-color: #DFF7F6;
    }
  }

  .tag-filter {
    width: 18px;
    height: 14px;
    margin-right: 12px;
  }

  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .content-table-list {
    background-color: #FFFFFF;

    .mes-content {
      display: flex;
      align-items: center;
      margin: 0 12px;
      height: 90px;

      .picture-box {
        position: relative;
        width: 90px;
        height: 100%;
        flex-shrink: 0;
        margin-right: 10px;

        .img-box {
          flex-shrink: 0;
          margin-right: 10px;
          width: 90px;
          height: 90px;
          border-radius: 5px;
          overflow: hidden;
          object-fit: cover;
        }

        .diff-box {
          position: absolute;
          top: 0;
          color: #FFFFFF;
          font-size: 13px;
          text-align: center;
          border-radius: 5px 0 5px 0;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          background: rgba(250, 81, 81, 0.6);

          .diff-text {
            padding: 4px;
          }
        }

        .cover-box {
          position: absolute;
          bottom: 0;
          color: #FFFFFF;
          font-size: 13px;
          width: 100%;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-bottom-left-radius: 5px;
          border-bottom-right-radius: 5px;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          background: rgba(61, 61, 61, 0.4);
        }

        .promotion-box {
          background: rgba(71, 142, 135, 0.5);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);

        }

        .expire-box {
          background: rgba(250, 81, 81, 0.5);
          backdrop-filter: blur(50px);
          -webkit-backdrop-filter: blur(10px);

        }
      }

      .image-top {
        position: absolute;
        top: 0;
        display: flex;
        align-items: center;
        border-radius: 5px 0px 5px 0px;
        color: #FFFFFF;
        font-size: 10px;
        font-weight: 400;

        .jd-icon {
          width: 9px;
          height: 10px;
          padding: 4px;
        }

        .cut-line {
          margin: 2px 4px;
          width: 1px;
          height: 10px;
          opacity: 0.3;
          background: #FFFFFF;
        }
      }

      .mes-box {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title-box {
          display: flex;
          align-items: center;
        }

        .mes-title {
          color: #3D3D3D;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.3;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }

        .mes-desc {
          margin-top: 6px;

          .train-tag {
            color: #FFFFFF;
            font-size: 10px;
            padding: 3px;
            border-radius: 5px;
            background: #9673FF;
            margin-right: 3px;
          }

          .mes-tag {
            color: #FFFFFF;
            font-size: 10px;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px 0;
            width: 37px;
            margin-right: 4px;
            border-radius: 3px;
            background-color: #00B578;

            .tag-icon {
              width: 9px;
              height: 10px;
              margin-right: 2px;
            }
          }

          .red-tag {
            background-color: #FA5151;
          }

          .mes-time {
            color: #999999;
            font-size: 11px;
            font-weight: 400;
          }
        }

        .brand-box {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .brand-left {
          display: flex;
          align-items: center;

          .brand-icon {
            width: 40px;
            height: 18px;
            border-radius: 3px;
          }

          .brand-name {
            color: #666666;
            font-size: 10px;
            font-weight: 400;
            padding-left: 5px;
          }
        }

        .brand-right {
          display: flex;
          align-items: center;
          color: #999999;
          font-size: 10px;
          font-weight: 400;

          .eye-icon {
            width: 16px;
            height: 16px;
            margin-right: 2px;
          }
        }

        .user-box {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .user-left {
            display: flex;
            align-items: center;

            .user-avatar {
              width: 24px;
              height: 24px;
              overflow: hidden;
              border-radius: 50%;
            }

            .user-name {
              color: #999999;
              font-size: 13px;
              font-weight: 400;
              padding-left: 5px;
            }
          }

          .user-right {
            display: flex;
            align-items: center;
            color: #999999;
            font-size: 10px;
            font-weight: 400;

            .eye-icon {
              width: 16px;
              height: 16px;
              margin-right: 2px;
            }
          }
        }
      }
      .mes-text-box {
        height: 98%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .item-name {
          color: #3D3D3D;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.3;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .item-time {
          color: #999999;
          font-size: 11px;
          font-weight: 400;
        }
      }
    }
  }

  .posted-btn {
    position: fixed;
    right: 12px;
    top: 75%;
    z-index: 100;

    .posted-box {
      width: 40px;
      height: 40px;

      .posted-img {
        width: 28px;
        height: 28px;
      }
    }
  }
}

.empty-wrap {
  padding-top: 40%
}
</style>