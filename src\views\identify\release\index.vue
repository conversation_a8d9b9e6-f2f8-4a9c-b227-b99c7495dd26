<template>
  <div class="release-page" :class="{'release-page-fixed': showPopover}" :style="{ paddingTop: `${titleHeight}px`}">
    <div v-if="modifyTip" class="modify-box relative" @click="goToHome">
      <div>参照系统推荐发布，有助快速鉴定</div>
      <div class="btn-text">查看</div>
      <img class="modify-icon" :src="tipClose" @click.stop="modifyClose"/>
    </div>
    <div class="release-header">
      <img v-if="brandLogo" class="header-img" :src="brandLogo">
      <img v-else class="header-img" :src="brandOther">
      <div>{{ `${brand}：${category}` }}</div>
    </div>
    <div class="release-container">
      <TitleTop title="添加求鉴图片（必填）">
        <template #right>
          <div class="title-right" @click="changePhotograph">拍摄方法
            <van-icon class="ml-2" name="arrow" color="#999999" size="14"/>
          </div>
        </template>
      </TitleTop>
      <div class="picture-tips mt-10">
        <div>1.鉴定可能受拍摄角度、清晰度等因素影响，请提供满足拍摄的实拍图</div>
        <div class="mt-5">2.请按照拍摄模板顺序依次拍摄，带星号为必传，避免影响鉴别结论</div>
        <div class="mt-5" style="color: #FA5151">3.注意：若发现多件装备混合发布求鉴的，将视为拼图直接屏蔽</div>
      </div>
      <div v-if="editType && pageId" class="repair-tips picture-tips">注意：其他补充总的最多可补六张图片
      </div>
      <div v-if="newList.length" class="picture-list"
           :class="{'picture-list-col' : btnText === '发布补图' || category === '其他' || authStore.phone}">
        <div
            class="picture-container"
            v-for="(item, index) in newList"
            :key="index"
            @click="handleUpdate(item, index)"
            :class="{'picture-box' : item.path || item.icon}"
        >
          <img
              v-if="hasImage(item)"
              class="item-img"
              :src="getImageSrc(item)"
              alt="图片"
          />
          <img
              v-if="!hasImage(item) && item.icon"
              class="item-icon"
              :src="item.icon"
              alt="图标"
          />
          <div
              v-if="!hasImage(item)"
              class="item-name"
              :class="{ 'must-item': item.require }"
          >
            {{ item.require ? `${item.name}*` : item.name }}
          </div>
          <div v-if="!item.censor" class="censor-box">违规</div>
          <div v-if="item.cover" class="cover-box">当前封面</div>
          <div v-if="item.exampleIcon && !authStore.phone" class="example-text" @click.stop="handleExample(index)">
            示例图
          </div>
        </div>
      </div>
      <div class="mt-20 mb-6">
        <TitleTop title="装备名称（必填）" :right="false"/>
      </div>
      <div style="border: 1px dashed #A5A5A5">
        <van-field v-model="equipmentName" :readonly="editType" type="textarea" placeholder="请输入装备名称或别名"
                   maxlength="40" :formatter="formatter" @click="handleField"/>
      </div>
      <div v-if="censorContent && !hasNonLocal" class="censor-content">{{
          `此文案“${censorContent}”已违规，请更正`
        }}
      </div>
      <div v-if="hasNonLocal" class="censor-content">{{ `温馨提示：内容中有发现敏感词汇请更正后再试` }}</div>
      <div class="equipment-box">
        <div class="equ-left">
          <div class="left-name" :class="{'left-name-active': equipName}" @click="changeEquip">
            <div v-if="!equipName"> # 关联装备</div>
            <div v-if="equipName">{{ `# ${equipName}` }}</div>
          </div>
          <div v-if="!equipName" class="left-tips" @click="changeEquip">关联后可获得更多曝光</div>
          <img v-if="equipName" class="del-icon" :src="delIcon" @click="delEquip"/>
        </div>
        <div class="equ-right" @click="changeEquip">
          <span>{{ equipName ? '已关联' : '去关联' }}</span>
          <van-icon class="ml-3" name="arrow" color="#999999" size="12"/>
        </div>
      </div>
      <div class="mt-20 mb-6">
        <TitleTop title="其他描述" :right="false">
          <template #right>
            <div class="title-right">{{ `${describeLength}/200` }}</div>
          </template>
        </TitleTop>
      </div>
      <div style="border: 1px dashed #A5A5A5">
        <SvInputAt class="editor-box" ref="svInputAtRef" :height="'94px'" @at="handleAt" @input="handleInput"
                   @click="handleField" :editable="editType ? false : 'plaintext-only'" maxLength="200"
                   v-model:html="htmlContent" :atNumber="12" @atExceed="handleAtExceed"
                   placeholder="请补充其他说明及价格有助于更快完成鉴定，请不要说出购买商家"
        />
      </div>
      <div class="mt-20 mb-8" v-if="redReward">
        <TitleTop :title="payCount && !detailInfo?.id ? '红包奖励（必选）' : '红包奖励'" :right="false">
          <template #right>
            <div class="title-right" @click="handleRule">红包规则
              <img class="doubt-img" :src="doubtIcon"/>
            </div>
          </template>
        </TitleTop>
      </div>
      <div class="red-packet-box" v-if="redReward">
        <div class="red-item-box"
             :class="{'red-item-box-active': item.id === moneyId}"
             v-for="item in redPacketSum" :key="item.id" @click="handleMoney(item.id)">
          <div class="red-item-money" :class="{'red-item-active': item.id === moneyId}"><span
              style="font-size: 10px">¥</span>{{ item.money }}.00
          </div>
          <div class="red-item-count" :class="{'red-item-active': item.id === moneyId}">{{ `${item.count}个红包` }}
          </div>
          <img v-if="item.id === moneyId" class="check-img" :src="redChecked"/>
        </div>
      </div>
      <div class="guide-box pt-10 pb-14">
        <img class="guide-icon mr-8" :src="checkFlag ? checkedIcon :unCheckIcon" @click="changeCheck">
        <div class="mt-1">已同意并阅读<span style="color: #5070D6" @click="handleGuide">《鉴定须知》</span></div>
      </div>
    </div>

    <div class="footer-box">
      <div class="count-text" v-if="payCount > 0 && !detailInfo?.id">
        {{ `支付红包奖励${moneyId}元，今日第${payCount}次付费求鉴${calculateAppraisalFee(payCount)}元` }}
      </div>
      <div class="count-text" v-if="detailInfo?.id && moneyId">{{ `支付红包奖励${moneyId}元` }}</div>
      <div class="release-footer flex-col-hc" @click="handleReleaseChange">
        <!--        <div class="btn-text pt-11" :class="{'pb-11': !btnTips}">{{ btnText }}</div>-->
        <div class="btn-text pt-14 pb-14">{{ btnText }}</div>
        <!--        <div v-if="btnTips" class="btn-tips pt-2 pb-8">{{ btnTips }}</div>-->
      </div>
    </div>
    <Loading ref="subLoadingRef" class="loading-box"/>
    <UploadPopup ref="uploadPopupRef" :popShow="false" @upload-success="handleSuccess"/>
    <DelPopup ref="delPopupRef" :item="moreItem" :type="btnText" @del="delImg" @change="changeImg"
              @appChange="appChangeImg" @cover="changeCover"/>
    <appUpPopup ref="appPopupRef" @appChange="appChangeImg"/>
    <PublishPopup ref="publishPopupRef" @jump="handleJump"/>
    <MentionPopup ref="mentionPopupRef" @addUser="handleUser" :brandId="brandId" :cateId="categoryId"/>
    <EquipmentPopup ref="equipmentPopupRef" :brandId="brandId" :cateId="categoryId" @addEquip="handleAddEquip"
                    @cannotFind="handleCannotFind"/>
    <RedPacketRule ref="redPacketRuleRef"/>
    <CDialog
        ref="commonDialogRef"
        title="温馨提示"
        :btnRightTxt="dialogBtnText"
        :content="dialogText"
        @rightClick="handleConfirm"
    />
    <div class="preview-dialog">
      <van-image-preview
          v-model:show="showPopover"
          :images="getImages()"
          :start-position="preIndex"
          swipe-duration="500"
          :loop="false"
          @change="onChange"
          @close="onClose"
          class="preview-container"
      >
        <template v-slot:index>
          <div class="swipe-title">
            <div class="swipe-tip">请按示例图位置上传高清图片否则影响鉴定</div>
            <dvi class="desc-box">
              <div class="desc-left">
                <img class="desc-icon" :src="preIcon"/>
              </div>
              <div class="desc-right">
                <div>{{ imgTitle }}</div>
                <div class="desc-text">示例图</div>
              </div>
            </dvi>
          </div>
        </template>
        <template #image="{ src, onLoad }">
          <img :src="src" :style="[{ width: '100%',  paddingBottom: '10%' }]"/>
        </template>
      </van-image-preview>
      <!--      && authStore.phone-->
      <div class="btn-box" v-if="showPopover ">
        <div class="btn mr-13" @click="handleClap('camera')">拍摄上传</div>
        <div class="btn " @click="handleClap('album')">从相册上传</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed} from "vue";
import TitleTop from "@/components/titleTop.vue";
import UploadPopup from "@/views/identify/components/uploadPopup.vue";
import DelPopup from "@/views/identify/components/delPopup.vue";
import appUpPopup from "@/views/identify/components/appUpload.vue"
import CDialog from "@/components/c-dialog.vue";
import PublishPopup from "@/views/identify/components/publishPopup.vue";
import MentionPopup from "@/views/identify/components/mentionPopup.vue";
import EquipmentPopup from "@/views/identify/components/equipmentPopup.vue";
import RedPacketRule from "@/views/identify/components/redPacketRule.vue";
import SvInputAt from "@/views/identify/release/components/sv-input-at.vue";
import {detectDevice, getQueryParam} from "@/utils/common.js";
import {otherList, categoryBrandMap, commonCategory} from '@/views/identify/lists'
import brandOther from "@/assets/images/details/brandOther.png"
import tipClose from "@/assets/images/common/tipClose.png"
import checkedIcon from '@/assets/images/common/checkedIcon.png'
import doubtIcon from '@/assets/images/reward/doubt.png'
import redChecked from '@/assets/images/reward/redChecked.png'
import unCheckIcon from '@/assets/images/common/unCheckIcon.png'
import addIcon from "@/assets/images/identify/addIcon.png";
import delIcon from "@/assets/images/identify/eqdelIcon.png"
import {debounce} from "lodash";
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();
import {useRoute, useRouter} from "vue-router";
import {showLoadingToast, showToast} from "vant";
import {isMagApp} from "@/utils/common.js";
import {getCache} from "@/utils/cache.js";


const route = useRoute()
const router = useRouter()

// 判断是否有图片路径
const hasImage = (item) => {
  return !!item.path; // 只需检查 path 是否存在
};

// 获取图片路径
const getImageSrc = (item) => {
  return item.path; // 直接返回 path
};

const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const showPopover = ref(false);
const redReward = ref(false)
const imgTitle = ref('');
const preIndex = ref(0);
const preIcon = ref('');
const imageList = [];
const svInputAtRef = ref()
const htmlContent = ref('')
const pageId = ref()
const moreItem = ref()
const imageName = ref('')
const imageIndex = ref(0)
const eaNumber = ref(0)
const listMax = ref(0)
const releaseId = ref()
const partition = ref()
const deepImageId = ref()
const repairList = ref()
const extraId = ref()
const detailInfo = ref()
const brand = ref()
const brandId = ref()
const category = ref()
const categoryId = ref()
const brandLogo = ref()
const editType = ref(false)
const contentTips = ref(true)
const newList = ref([])
const attach = ref([]) //图片id
const censorContent = ref('') //违规词
const hasNonLocal = ref(false)
const equipmentName = ref('')
const editEquip = ref(false) //是否是修改装备
const equipTips = ref(true) //是否提示
const equipName = ref('')  //关联装备
const equipId = ref('')  //关联装备
const describeLength = ref(0)
const uploadPopupRef = ref()
const subLoadingRef = ref()
const delPopupRef = ref()
const appPopupRef = ref()
const publishPopupRef = ref()
const mentionPopupRef = ref()
const equipmentPopupRef = ref()
const redPacketRuleRef = ref()
const prevAtCount = ref(0) //@出现的次数
const commonDialogRef = ref()
const checkFlag = ref(false)
const moneyId = ref(0)
const payCount = ref(0) //付费次数
const modifyTip = ref(true)
const releaseGold = ref()
const btnText = ref('免费求鉴')
// const btnTips = ref('每日可免费发布1次')
const dialogText = ref('发布后有人表态不可修改 确定发布？')
const dialogBtnText = ref('确定')


// 用户信息
const userInfo = computed(() => {
  return authStore.userInfo
})

const formatter = (value) => value.replace(/[\r\n]/g, '')

const changePhotograph = () => {
  if (isMagApp()) {
    window.mag.newWin('https://app.badmintoncn.com//mag/circle/v1/forum/threadViewPage?tid=607549')
  } else {
    window.location.href = 'https://app.badmintoncn.com//mag/circle/v1/forum/threadViewPage?tid=607549'
  }
}

const redPacketSum = ref([
  {id: 2, money: 2.00, count: 4},
  {id: 5, money: 5.00, count: 10},
  {id: 10, money: 10.00, count: 20},
  {id: 15, money: 15.00, count: 30},
])

const handleExample = (index) => {
  preIndex.value = index
  showPopover.value = !showPopover.value
  imgTitle.value = newList.value[index].name
  preIcon.value = newList.value[index].icon
}

const onChange = (newIndex) => {
  imageIndex.value = newIndex
  imageName.value = newList.value[newIndex].name
  preIndex.value = newIndex
  preIcon.value = newList.value[newIndex].icon
  imgTitle.value = newList.value[newIndex].name
};

const onClose = () => {
  // window.mag.showNavigation();
}

const getImages = () => {
  let list = []
  newList.value.forEach((item) => {
    if (item.exampleIcon) {
      list.push(item.exampleIcon)
    }
  })
  return list
}

const uploadApp = async (file) => {
  showLoadingToast({message: '处理中...', forbidClick: true})
  let res = await identifyApi.uploadMap({
    path: file.url
  });
  if (res.code === 200) {
    handleSuccess(res.data)
  }
}

const handleClap = (type) => {
  if (type === 'camera') {
    window.mag.camera({
      success: function (res) {
        console.log('success', res)
        uploadApp(res)
      },
    });
  } else if (type === 'album') {
    window.mag.picPick({
      limit_count: 1,
      success: function (res) {
        console.log('success', res)
        uploadApp(res)
      },
    });
  }
  showPopover.value = false
}

const handleUpdate = (item, index) => {
  // editType.value = true
  // console.log('item', item)
  // return;
  if (editType.value && item.path && !item.file && item.censor && item.id && !item.del) {
    showToast('当前为补图状态只能补图')
    return
  }
  imageIndex.value = index
  imageName.value = item.name
  moreItem.value = item
  if (item.path) {
    delPopupRef.value.open()
    return
  }
  if (authStore.phone) {
    if (imageName.value === '其他补充' || category.value === '其他') {
      appPopupRef.value.open()
      return;
    } else {
      preIndex.value = index
      showPopover.value = !showPopover.value
      imgTitle.value = newList.value[index].name
      preIcon.value = newList.value[index].icon
    }
  } else {
    // if (item.path) {
    //   delPopupRef.value.open()
    //   return
    // }
    uploadPopupRef.value.open()
  }
}

const handleUser = (list) => {
  svInputAtRef.value?.insertAts(list);
  // const userAt = svInputAtRef.value.getAllAtLink();
  // if (userAt.length) {
  //   const isUserAlreadyAt = userAt.some(item => item.id === res.id);
  //   if (!isUserAlreadyAt) {
  //     svInputAtRef.value.insertAt(res);
  //   } else {
  //     setTimeout(() => {
  //       svInputAtRef.value.deleteString('@')
  //     })
  //     showToast('您已经@过TA了')
  //   }
  // } else {
  //   svInputAtRef.value.insertAt(res);
  // }
  let val = svInputAtRef.value.getContent()
  const plainText = val.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ');
  describeLength.value = plainText.trim().length
}

const validateItems = (items) => {
  for (let item of items) {
    if (item.require && !item.path) {
      return false;
    }
  }
  return true;
}

const getFileId = async (list) => {
  const resultItems = [];
  list.forEach((item, index) => {
    if (item.id) {
      resultItems.push({
        id: item.id,
        cover: item.cover || false
      });
    } else {
      resultItems.push({
        id: 0,
        cover: false
      });
    }
  });
  while (
      resultItems.length > 0 &&
      resultItems[resultItems.length - 1].id === 0 &&
      resultItems[resultItems.length - 1].cover === false
      ) {
    resultItems.pop();
  }
  console.log('resultItems', resultItems);
  return resultItems;
};

const getCoverId = async (list) => {
  const coverTrueItem = list.find(item => item.cover === true);
  return coverTrueItem ? coverTrueItem.id : list[0].id;
}

const handleRepair = async () => {
  repairList.value = await getFileId(newList.value)
  const idList = deepImageId.value.split(',').map(Number);
  const difference = repairList.value.filter(item => !idList.includes(item.id));
  let imgIds = difference.map(item => item.id) // 提取所有 id
  if (!difference.length || imgIds.join(',') === extraId.value) {
    showToast('当前无任何变更')
    setTimeout(() => {
      releaseId.value = detailInfo.value.id
      partition.value = detailInfo.value?.partition
      return handleJump()
    }, 1000)
    return
  } else {
    commonDialogRef.value.show()
  }

}

const confirmRepair = async () => {
  const idList = deepImageId.value.split(',').map(Number);
  const difference = repairList.value.filter(item => !idList.includes(item.id));
  let imgIds = difference.map(item => item.id) // 提取所有 id
  let res = await identifyApi.repairIdentify({
    tid: detailInfo.value.id,
    attach: imgIds.join(',')
  })
  if (res.code === 200) {
    // showToast('补图成功')
    releaseId.value = detailInfo.value.id
    partition.value = detailInfo.value?.partition
    subLoadingRef.value.hide()
    // handleJump()
    publishPopupRef.value.open()
  } else {
    subLoadingRef.value.hide()
  }
}

const handleReleaseChange = debounce(async () => {
  await handleRelease()

}, 400)

//确认发布
const handleRelease = async () => {
  if (Number(categoryId.value) !== 0 && Number(brandId.value) !== 0) {
    if (!equipId.value && equipTips.value && equipTips.value) {
      showToast('请关联装备即可发布')
      // equipTips.value = false
      return
    }
  }
  if (!checkFlag.value) {
    showToast('请勾选并同意相关政策')
    return
  }
  if (btnText.value === '发布补图') {
    //补图
    handleRepair()
    return
  }
  if (validateItems(newList.value) && equipmentName.value) {
    if (btnText.value === '发布修改') {
      dialogText.value = '发布后有人表态不可修改 确定发布？'
    } else {
      dialogText.value = '发布后有人表态不可修改'
      // dialogText.value = releaseGold.value ? [
      //   '发布后有人表态不可修改',
      //   `发布将扣除${releaseGold.value}金币，确定发布？`,
      // ] : '发布后有人表态不可修改 确定发布？'
    }
    dialogBtnText.value = '确定'
    commonDialogRef.value.show()
  } else {
    showToast('请填写或上传完整信息')
  }
}

const handleSuccess = (file) => {
  if (file) {
    const currentItem = newList.value.find((item, index) =>
        item.name === imageName.value && index === imageIndex.value
    );
    if (currentItem) {
      currentItem.path = file.src;
      currentItem.censor = file.censor;
      currentItem.id = file.id;
      currentItem.del = true;
      if (newList.value[0] === currentItem) {
        // 设置当前为封面
        currentItem.cover = true;
      }
    }
    const supplementButtonIndex = newList.value.findIndex(
        item => item.name === '其他补充' && !item.path
    );
    if (supplementButtonIndex > -1) {
      newList.value.splice(supplementButtonIndex, 1);
    }
    const supplementCount = newList.value.filter(
        item => item.name === '其他补充' && item.path
    ).length;
    if (supplementCount < 6) {
      newList.value.push({
        name: '其他补充',
        icon: addIcon,
        censor: true,
        require: false
      });
    }
  }
}

const delImg = () => {
  delPopupRef.value.hide();
  const findIndex = newList.value.findIndex(
      (item, index) => item.name === imageName.value && index === imageIndex.value
  );
  if (findIndex === -1) return;
  if (newList.value[findIndex].name === '其他补充') {
    newList.value.splice(findIndex, 1);
    const supplementItems = newList.value.filter(
        item => item.name === '其他补充' && item.path
    );
    if (supplementItems.length < 6) {
      const hasSupplementButton = newList.value.some(
          item => item.name === '其他补充' && !item.path
      );
      if (!hasSupplementButton) {
        newList.value.push({
          name: '其他补充',
          icon: addIcon,
          censor: true,
          require: false
        });
      }
    }
    if (supplementItems.length >= 6) {
      newList.value = newList.value.filter(
          item => !(item.name === '其他补充' && !item.path)
      );
    }
  } else {
    newList.value[findIndex].path = ''
    newList.value[findIndex].id = ''
    newList.value[findIndex].censor = true
    newList.value[findIndex].cover = false
  }
}

//设置为封面
// const changeCover = () => {
//   delPopupRef.value.hide()
//   console.log('newList.value', newList.value)
//   const findIndex = newList.value.findIndex((item, index) => item.name === imageName.value && index === imageIndex.value)
//   newList.value[findIndex].cover = !newList.value[findIndex].cover
// }
const changeCover = () => {
  delPopupRef.value.hide(); // 隐藏弹窗
  const findIndex = newList.value.findIndex(
      (item, index) => item.name === imageName.value && index === imageIndex.value
  );
  if (findIndex !== -1) {
    const currentItem = newList.value[findIndex];
    if (currentItem === newList.value[0] && currentItem.cover) {
      showToast('默认封面不可取消')
    }
    if (currentItem.cover) {
      currentItem.cover = false;
    } else {
      newList.value.forEach(item => {
        item.cover = false;
      });
      currentItem.cover = true;
    }
    const hasCover = newList.value.some(item => item.cover);
    if (!hasCover && newList.value.length > 0) {
      // 强制设置第一个元素为封面
      newList.value[0].cover = true;
    }
  }
};

const changeImg = () => {
  delPopupRef.value.hide()
  uploadPopupRef.value.open()
}

const appChangeImg = (type) => {
  appPopupRef.value.hide()
  delPopupRef.value.hide()
  handleClap(type)
}

const handleJump = () => {
  if (authStore.phone) {
    if (btnText.value === '发布修改' || btnText.value === '发布补图') {
      window.mag.closeWin();
    } else {
      window.mag.closeWin();
      const apiUrl = authStore.apiUrl
      window.mag.newWin(`${apiUrl}/details/index?mag_hide_progress=1`, {
        id: releaseId.value,
        partition: partition.value
      });
    }
  } else {
    if (btnText.value === '发布修改' || btnText.value === '发布补图') {
      router.back()
    } else {
      router.push({
        name: 'details',
        query: {
          id: releaseId.value,
          partition: partition.value
        }
      })
    }


  }
}

const handleRule = () => {
  redPacketRuleRef.value.show()
}

const handleMoney = (id) => {
  if ((route.query.id || getQueryParam('id')) && detailInfo?.value?.redAmount === 0) {
    if (moneyId.value === id) {
      moneyId.value = 0
    } else {
      moneyId.value = id
    }
    if (moneyId.value) {
      btnText.value = `支付${moneyId.value}元求鉴`
    } else {
      btnText.value = `发布修改`
    }
  } else {
    moneyId.value = id
    btnText.value = `支付${calculateAppraisalFee(payCount.value) + moneyId.value}元求鉴`
  }
}

const changeCheck = () => {
  checkFlag.value = !checkFlag.value
}


const handleGuide = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/release/instructions?mag_hide_progress=1`);
  } else {
    router.push({
      name: 'instructions'
    })
  }
}


const modifyClose = () => {
  modifyTip.value = false
}

const goToHome = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/brand/index?mag_hide_progress=1`, {
      // brandKey: brand,
      stateKey: 4,
      // categoryKey: category,
      btnType: 1,
    });
  } else {
    router.push({
      name: 'brand',
      query: {
        // brandKey: brand,
        stateKey: 4,
        // categoryKey: category,
        btnType: 1,
      }
    })
  }
}

const convertLinksToTags = (str) => {
  const anchors = [];
  let index = 0;
  const tempStr = str.replace(/<a\s[^>]*>.*?<\/a>/gi, (match) => {
    anchors.push(match);  // 保存原始标签
    return `%%%ANCHOR_${index++}%%%`; // 用唯一占位符替换
  });
  const converted = tempStr.replace(
      /\bhttps?:\/\/(?:[a-z0-9-]+\.)*badmintoncn\.com\S*/gi,
      (url) => `<a href="${url}" target="_blank">${url}</a>`
  );
  return converted.replace(/%%%ANCHOR_(\d+)%%%/g, (_, idx) => anchors[idx]);
};

const filterSpaces = (text) => {
  return text.trim().length > 0;
}

//确认退出
const handleConfirm = async () => {
  if (btnText.value === '发布补图') {
    //补图
    if (!editEquip.value) {
      confirmRepair()
      return
    }
  }
  if (dialogBtnText.value === '确定' && releaseGold.value === 0) {
    await handleReleasePost()
  } else if (dialogBtnText.value === '确定' && releaseGold.value > 0) {
    await handleReleasePost()
    // let res = await identifyApi.startPayment({
    //   // amount: releaseGold.value,
    //   amount: 0.01,
    //   des: `帖子${Number(calculateAppraisalFee(payCount.value))}元,红包${Number(moneyId.value)}元`
    // })
    // let data = res.data.list
    // let config = {
    //   money: data.money,
    //   title: data.title,
    //   des: data.des,
    //   orderNum: data.orderNum,
    //   unionOrderNum: data.unionOrderNum,
    // }
    // window.mag.pay(config, function () {
    //   // 支付成功回调
    //   console.log('支付成功')
    //   identifyApi.queryPayment({
    //     tradeNo: data.unionOrderNum
    //   }).then((res) => {
    //     console.log('res====', res)
    //     if (Number(res?.data?.list) === 1) {
    //       handleReleasePost()
    //     }
    //   })
    // }, function () {
    //   // 支付失败回调
    // });
  } else {
    if (isMagApp()) {
      window.mag.newWin(`https://www.badmintoncn.com/jq.php?mag_hide_progress=1`);
    } else {
      window.location.href = 'https://www.badmintoncn.com/jq.php?mag_hide_progress=1'
    }
  }
}


//发布帖子
const handleReleasePost = async () => {
  // subLoadingRef.value.show()
  // let sub = svInputAtRef.value.getContent()
  let str = svInputAtRef.value?.getContent() || ''
  let sub = str ? convertLinksToTags(str) : ''
  let subContent = ''
  if (filterSpaces(sub) && sub !== '<br>') {
    subContent = sub
  }
  let user = svInputAtRef.value.getAllAtLink()
  let imgList = await getFileId(newList.value)
  let coverId = await getCoverId(imgList)
  let imgIds = imgList.map(item => item.id) // 提取所有 id
  let subForm = {
    uid: userInfo.value.id, //用户id
    brand: brandId.value,
    cate: categoryId.value,
    attach: imgIds.join(','),
    at: user.length ? user?.map(item => item.id).join(',') : '',
    name: equipmentName.value,
    content: subContent ? subContent : '',
    id: detailInfo?.value?.id || '', //可选，编辑的时候传
    partition: detailInfo?.value?.partition || '', //可选，编辑的时候传
    from: detectDevice(),
    cover: coverId,
    eid: equipId?.value || '',
    pay: detailInfo?.value?.id ? '' : calculateAppraisalFee(payCount.value),
    red: moneyId.value > 0 ? moneyId.value : ''
  }
  console.log('subForm', subForm)
  let res = await identifyApi.addIdentify(subForm)
  if (res.code === 200) {
    releaseId.value = res.data.list
    partition.value = res.data.total
    // if (detailInfo?.value?.id) return
    subLoadingRef.value.hide()
    if (!editEquip.value) {
      publishPopupRef.value.open()
    } else {
      editEquip.value = false
      if (equipId.value) {
        showToast('已关联装备')
      }
    }
  } else {
    subLoadingRef.value.hide()
    showToast(res.message || '上传失败')
  }
  // else if (res.code === 4000) {
  //   subLoadingRef.value.hide()
  //   dialogText.value = '当前金币余额不足 充值后方可继续发布'
  //   dialogBtnText.value = '去充值'
  //   commonDialogRef.value.show()
  //   // showToast(res.message)
  // }

}

const handleAt = () => {
  let userAt = svInputAtRef.value?.getAllAtLink()
  if (userAt.length >= 12) {
    showToast('@人数已达上限')
    return
  }
  svInputAtRef.value?.blur()
  mentionPopupRef.value.open(userAt)
}


const handleAtExceed = () => {
  svInputAtRef.value.blur()
  showToast('@人数已达上限')
}


const handleField = () => {
  if (editType.value) {
    showToast('当前为补图状态只能补图')
  }
}

const delEquip = () => {
  equipName.value = ''
  equipId.value = ''
  if (btnText.value === '发布修改' || btnText.value === '发布补图') {
    editEquip.value = true
    handleConfirm()
  }
}

const changeEquip = () => {
  let obj = {
    brandId: brandId.value,
    cateId: categoryId.value,
    keyword: equipmentName.value,
  }
  equipmentPopupRef.value.open(obj, equipId.value)
}

const handleAddEquip = (obj) => {
  if (!equipmentName.value) {
    equipmentName.value = obj.equipName
  }
  equipName.value = obj.equipName
  equipId.value = obj.equipId
  if (btnText.value === '发布修改' || btnText.value === '发布补图') {
    editEquip.value = true
    handleConfirm()
  }
}


const handleCannotFind = () => {
  equipTips.value = false
}

const handleInput = (val) => {
  describeLength.value = val.length
}


// const calculateGold = (value) => {
//   if (!Number.isInteger(value)) {
//     throw new Error("Value must be an integer");
//   }
//   if (value < 0) {
//     throw new Error("Value cannot be negative");
//   }
//   if (value === 0) {
//     return 0;
//   } else if (value > 100) {
//     throw new Error("Value too large to compute gold amount");
//   } else {
//     return 100 * Math.pow(2, value - 1);
//   }
// }

const calculateAppraisalFee = (times) => {
  if (times === 0) return 0
  const fee = 2 ** times;
  return fee;
}

const getEaNumber = async () => {
  let res = await identifyApi.getEaCount({
    uid: userInfo.value.id,
    type: 1
  })
  if (res.code === 200) {
    payCount.value = res.data.list
    if (payCount.value === 0) return
    if (!route.query.id || !getQueryParam('id')) {
      moneyId.value = 2
    }
    btnText.value = `支付${calculateAppraisalFee(payCount.value) + moneyId.value}元求鉴`
    releaseGold.value = calculateAppraisalFee(payCount.value) + moneyId.value
    // releaseGold.value = calculateGold(res.data.list)
    // // btnTips.value = `${calculateGold(res.data.list)}金币且获得优先曝光`
  }
}


// const mergeArrays = (all, partial) => {
//   const result = [];
//   all.forEach((item, index) => {
//     if (partial[index]) {
//       // 如果有对应的项，则合并数据
//       result.push({
//         name: item.name,
//         icon: item.icon,
//         require: item.require,
//         id: partial[index].id,
//         path: partial[index].path,
//         createTime: partial[index].createTime
//       });
//     } else {
//       // 如果没有对应的项，则只添加原始数据
//       // result.push(item);
//       result.push({
//         name: item.name,
//         icon: item.icon,
//         require: item.require,
//       });
//     }
//   });
//   return result;
// };

const mergeArrays = (all, partial) => {
  const result = [];
  all?.forEach((item, index) => {
    if (partial[index]) {
      // 如果有对应的项，则合并数据
      result.push({
        name: item.name,
        icon: item.icon,
        exampleIcon: item.exampleIcon,
        require: item.require,
        id: partial[index].id,
        path: partial[index].path,
        cover: partial[index].cover,
        censor: partial[index].path ? partial[index].censor : true,
        createTime: partial[index].createTime,
      });
    } else {
      result.push({
        name: item.name,
        icon: item.icon,
        exampleIcon: item.exampleIcon,
        censor: item.path ? item.censor : true,
        require: item.require,
        cover: item.cover,
      });
    }
  });
  if (partial.length > all.length) {
    for (let i = all.length; i < partial.length; i++) {
      const extraItem = partial[i];
      if (extraItem.path) {
        result.push({
          name: "其他补充",
          icon: "",
          require: false,
          id: extraItem.id,
          exampleIcon: extraItem.exampleIcon,
          path: extraItem.path,
          censor: extraItem.path ? extraItem.censor : true,
          createTime: extraItem.createTime,
        });
      }
    }
  }
  console.log('result', result)
  return result;
};

const mergeRepairArrays = (partial, all) => {
  const result = [];
  partial.forEach((item, index) => {
    if (all[index] && partial[index].path && !partial[index].extra) {
      result.push({
        name: all[index].name,
        icon: all[index].icon,
        exampleIcon: item.exampleIcon,
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        cover: partial[index].cover,
        censor: partial[index].path ? partial[index].censor : true,
        createTime: partial[index].createTime
      });
    } else if (partial[index].path) {
      result.push({
        name: item.name || '其他补充',
        // icon: item.icon || addIcon,
        exampleIcon: item.exampleIcon,
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        cover: partial[index].cover,
        censor: partial[index].path ? partial[index].censor : true,
        createTime: partial[index].createTime
      });
    }
  });
  return result;
};


const getList = (category, brand) => {
  let list = categoryBrandMap[category]?.[brand] ?? categoryBrandMap[category] ?? otherList.value;
  if (detailInfo.value?.attachList) {
    newList.value = mergeArrays(list, detailInfo.value.attachList);
  } else {
    newList.value = list
  }
  if (newList.value[newList.value.length - 1].path && btnText.value === '发布修改') {

    let filteredArray = newList.value.filter(item => item.name === "其他补充");
    if (filteredArray.length < 6) {
      newList.value.push({
        name: '其他补充',
        icon: addIcon,
        censor: true,
        require: false
      });
    }
    listMax.value = 6 - filteredArray.length
    return
  }
  listMax.value = newList.value.length + 5
}

//补图
const getRepairList = (category, brand) => {
  let list = categoryBrandMap[category]?.[brand] ?? categoryBrandMap[category] ?? otherList.value;
  if (detailInfo.value) {
    // newList.value = mergeRepairArrays(list, detailInfo.value.attachList);
    newList.value = mergeRepairArrays(detailInfo.value.attachList, list);
  } else {
    newList.value = list
  }
  let filteredArray = newList.value.filter(item => item.name === "其他补充");
  console.log('filteredArray', filteredArray)
  if (filteredArray.length < 6) {
    newList.value.push({
      name: '其他补充',
      icon: addIcon,
      censor: true,
      require: false
    });
  }
  listMax.value = 6 - filteredArray.length
}

const getDetails = async (id) => {
  let res = await identifyApi.getEaDetails({id: id, uid: userInfo.value.id})
  const {code, data} = res
  if (code === 200) {
    detailInfo.value = data.list
    brand.value = detailInfo.value.brandName
    redReward.value = detailInfo.value.redAmount === 0
    brandId.value = detailInfo.value.brand
    category.value = detailInfo.value.cateName
    categoryId.value = detailInfo.value.cate
    brandLogo.value = detailInfo.value.brandLogo
    equipmentName.value = detailInfo.value.name
    htmlContent.value = detailInfo.value.content
    equipId.value = detailInfo.value?.eid
    equipName.value = detailInfo.value?.eName
    describeLength.value = htmlContent.value.replace(/<[^>]*>/g, '').length;
    let censorList = []
    if (detailInfo.value?.censorContent?.length) {
      detailInfo.value.censorContent.some((res) => {
        if (res.from === 'local') {
          censorList.push(res.content);
        } else {
          hasNonLocal.value = true;
          return true;
        }
        return false;
      });
    }
    censorContent.value = censorList.join(',')
    if (!detailInfo.value.editable) {
      btnText.value = '发布补图'
      if (authStore.phone) {
        window.mag.setTitle('发布补图');
      } else {
        document.title = '发布补图'
      }
      // btnTips.value = ''
      editType.value = true
      deepImageId.value = detailInfo.value.attach
      extraId.value = detailInfo.value.extra
      getRepairList(detailInfo.value.cateName, detailInfo.value.brandName)
    } else {
      btnText.value = '发布修改'
      if (authStore.phone) {
        window.mag.setTitle('发布修改');
      } else {
        document.title = '发布修改'
      }
      // btnTips.value = ''
      editType.value = false
      getList(detailInfo.value.cateName, detailInfo.value.brandName)
    }
    window.mag.showNavigation()
  }
}


const brandList = ref([
  {
    id: 1,
    name: "尤尼克斯",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/yonex.png"
  },
  {
    id: 22,
    name: "李宁",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/lining.png"
  },
  {
    id: 2,
    name: "威克多",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/victor.png"
  },
  {
    id: 0,
    name: "其他",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/others.png"
  },
])

const tagList = ref([]);

const categoryList = ref([]);

const getLogoById = (id) => {
  const brand = brandList.value.find(brand => brand.id === id);
  return brand ? brand.logo : null;
}

const showList = () => {
  // let cateList = JSON.parse(getCache('CATEGORY_LIST'))
  // categoryList.value = cateList.slice(1)
  categoryList.value = commonCategory.value.slice(1)
  let brand = JSON.parse(getCache('BRAND_LIST'))
  tagList.value = brand.slice(1)
}

const getNameById = (id, list = tagList.value) => {
  const item = list.find(item => item.id === id);
  return item ? item.name : null;
};

onMounted(async () => {
  await getEaNumber()
  await showList()
  if (route.query.categoryId || getQueryParam('categoryId')) {
    brandId.value = route.query.brandId || getQueryParam('brandId')
    brand.value = getNameById(Number(brandId.value))
    categoryId.value = route.query.categoryId || getQueryParam('categoryId')
    category.value = getNameById(Number(categoryId.value), categoryList.value)
    getList(category.value, brand.value)
    // brandLogo.value = route.query.logo || getQueryParam('logo')
    brandLogo.value = getLogoById(Number(brandId.value))
    if (authStore.phone) {
      window.mag.setTitle('发布求鉴');
    } else {
      document.title = '发布求鉴'
    }
    window.mag.showNavigation()
    redReward.value = true
  } else if (route.query.id || getQueryParam('id')) {
    contentTips.value = false
    pageId.value = route.query.id || getQueryParam('id')
    getDetails(route.query.id || getQueryParam('id'))
  }

  if (route.query.eid || getQueryParam('eid')) {
    let eName = route.query.eaName || getQueryParam('eaName')
    equipName.value = decodeURIComponent(eName)
    equipId.value = route.query.eid || getQueryParam('eid')
    if (!equipmentName.value) {
      equipmentName.value = equipName.value
    }
  }
  window.mag.hideMore()
  // window.mag.addRefreshComponent()
})

onUnmounted(() => {
  newList.value = []
})
</script>

<style scoped lang="scss">
.release-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  //overflow: hidden;
  box-sizing: border-box;
  background-color: #FFFFFF;

  .modify-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #FF853E;
    font-size: 12px;
    font-weight: 400;
    margin: 0 12px 10px;
    padding: 10px 30px 10px 12px;
    border-radius: 5px;
    background-color: #FFF4EE;

    .btn-text {
      padding: 3px 10px;
      font-size: 13px;
      font-weight: 600;
      border-radius: 15px;
      border: 1px solid #FF853E;
    }

    .modify-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 18px;
      height: 18px;
    }
  }

  .release-header {
    margin: 10px 12px;
    color: #3D3D3D;
    font-size: 13px;
    display: flex;
    align-items: center;

    .header-img {
      width: 40px;
      height: 18px;
      margin-right: 10px;
      border-radius: 3px;
    }
  }

  .release-container {
    margin: 15px 12px 100px;

    .title-right {
      color: #999999;
      font-size: 13px;
      font-weight: 400;
      display: flex;
      align-items: center;

      .doubt-img {
        width: 12px;
        height: 12px;
        margin-left: 5px;
      }
    }

    .picture-tips {
      color: #666666;
      font-size: 11px;
      font-weight: 400;
    }

    .repair-tips {
      margin-top: 8px;
      color: #FA5151;
    }

    .picture-list {
      margin: 12px 0 35px;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      gap: 32px 5px;

      .picture-container {
        display: none;
      }

      .picture-box {
        position: relative;
        width: calc((100% - 25px) / 4);
        border: 1px dashed #A5A5A5;
        //padding: 11px 0;
        height: 84px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .item-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .item-icon {
          width: 44px;
          height: 44px;
        }

        .item-name {
          color: #666666;
          margin-top: 8px;
          font-size: 11px;
          font-weight: 400;
        }

        .censor-box {
          position: absolute;
          top: 0;
          left: 0;
          width: 30px;
          height: 18px;
          line-height: 18px;
          text-align: center;
          background: #FBFF00;
          border: 2px solid #000000;
          font-size: 12px;
          font-weight: 800;
          color: #030303;
        }

        .cover-box {
          position: absolute;
          bottom: 0;
          color: #FFFFFF;
          font-size: 13px;
          width: 100%;
          height: 22px;
          line-height: 22px;
          text-align: center;
          background: rgba(61, 61, 61, 0.4);
        }

        .must-item {
          color: #FA5151;
        }
      }
    }

    .picture-list-col {
      gap: 5px;
      margin: 12px 0;
    }

    .guide-box {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-weight: 400;
      color: #3D3D3D;

      .guide-icon {
        width: 18px;
        height: 18px;
      }
    }
  }

  .censor-content {
    color: #FA5151;
    font-size: 13px;
    font-weight: 400;
    margin-top: 12px;
    margin-bottom: 5px;
  }

  .equipment-box {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .equ-left {
      display: flex;
      align-items: center;
      margin-right: 20px;

      .left-name {
        font-size: 13px;
        font-weight: 600;
        color: #FFFFFF;
        padding: 0 8px;
        height: 22px;
        line-height: 22px;
        border-radius: 3px;
        background-color: #5070D6;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .left-name-active {
        color: #5070D6;
        background-color: #EFF3FF;
      }

      .left-tips {
        margin-left: 5px;
        color: #666666;
        font-size: 13px;
        font-weight: 400;
        padding: 0 8px;
        height: 22px;
        line-height: 22px;
        border-radius: 3px;
        background-color: #EFF3FF;
      }

      .del-icon {
        width: 14px;
        height: 14px;
        margin-left: 5px;
      }
    }

    .equ-right {
      flex-shrink: 0;
      margin-left: 6px;
      font-weight: 400;
      display: flex;
      align-items: center;
      color: #999999;
      font-size: 13px;
    }
  }

  .red-packet-box {
    display: flex;
    flex: 1;
    flex-wrap: nowrap;
    justify-content: flex-start;
    column-gap: 5px;

    .red-item-box {
      position: relative;
      border-radius: 5px;
      background-color: #F5F5F5;
      width: calc((100% - 15px) / 4);
      font-weight: 400;
      text-align: center;
      padding: 9px 0;

      .red-item-money {
        font-size: 14px;
        font-weight: 600;
        color: #3D3D3D;
      }

      .red-item-count {
        margin-top: 8px;
        font-size: 12px;
        font-weight: 400;
        color: #666666;
      }

      .check-img {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 12px;
        height: 12px;
      }
    }

    .red-item-box-active {
      background-color: #FFE3DC;
    }

    .red-item-active {
      color: #FF3700 !important;
    }

    .red-item-box-active::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 200%;
      height: 200%;
      border-radius: 10px;
      border: 1px solid #FF805E;
      transform: scale(0.5);
      transform-origin: 0 0;
      box-sizing: border-box;
    }
  }

  .footer-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #FFFFFF;

    .count-text {
      padding-top: 4px;
      color: #666666;
      font-size: 12px;
      font-weight: 400;
      text-align: center;
    }

    .release-footer {
      margin: 15px 12px 20px;
      color: #FFFFFF;
      background-color: #478E87;
      border-radius: 22px;

      .btn-text {
        font-size: 16px;
        font-weight: 500;
      }

      .btn-tips {
        font-size: 10px;
        font-weight: 400;
      }
    }

  }

  .van-cell {
    padding: 8px;
  }
}

.release-page-fixed {
  overflow: hidden;
}

.editor-box {
  ::v-deep .input-at {
    height: 94px !important;
  }
}

// 添加新的类用于示例图文本
.example-text {
  position: absolute;
  bottom: -24px; // 根据需要调整位置
  left: 0;
  right: 0;
  text-align: center;
  color: #999999;
  font-size: 13px;
  font-weight: 400;
}

.example-icon {
  width: 100px;
  height: 100px;
}

.loading-box {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.btn-box {
  z-index: 99999;
  position: absolute;
  bottom: 120px;
  width: 100%;
  padding: 30px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;

  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    padding: 12px 0 11px;
    width: 100%;
    border: 1px solid #666666;
    background: #3D3D3D;
    border-radius: 22px 22px 22px 22px;
  }
}

.swipe-title {
  text-align: center;
  font-size: 16px;

  .swipe-tip {
    font-weight: 600;
    color: #FFF200;
    margin-bottom: 8px;
  }

  .desc-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .desc-left {
      margin-right: 12px;
      border-radius: 5px;
      background-color: #FFFFFF;

      .desc-icon {
        width: 36px;
        height: 36px;
        padding: 4px;
      }
    }

    .desc-right {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      color: #FFFFFF;
      font-size: 16px;
      font-weight: 600;

      .desc-text {
        margin-top: 5px;
        font-size: 13px;
        font-weight: 400;
      }
    }
  }
}

.preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.swipe-title {
  padding: 0 0 15px;
  text-align: center;
  font-size: 16px;

  .swipe-tip {
    font-weight: 600;
    color: #FFF200;
    margin-bottom: 8px;
    font-size: 14px;
  }
}

:deep(.van-image-preview__image-wrapper) {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 10px;
  box-sizing: border-box;
}

:deep(.van-image-preview__image) {
  max-width: 100%;
  max-height: calc(100vh - 200px);
  object-fit: contain;
}

.btn-box {
  position: fixed;
  bottom: 100px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 0 20px;
  z-index: 99999;
  gap: 15px;

  .btn {
    flex: 1;
    max-width: 45%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    padding: 12px 0;
    border: 1px solid #666666;
    background: #3D3D3D;
    border-radius: 22px;
  }
}

::v-deep .van-image-preview__index {
  width: 100%;
  top: 40px;
}

@media (max-height: 700px) {
  :deep(.van-image-preview__image) {
    max-height: calc(100vh - 180px);
  }

  .btn-box {
    bottom: 80px;
  }

  .swipe-title {
    padding: 0 0 8px;

    .swipe-tip {
      font-size: 12px;
      margin-bottom: 5px;
    }
  }
  :deep(.van-image-preview__index) {
    width: 100%;
    top: 30px;
  }
}

@media (max-width: 375px) {
  .btn-box .btn {
    font-size: 14px;
    padding: 10px 0;
  }

  .desc-box .desc-right {
    font-size: 14px;
  }
}
</style>